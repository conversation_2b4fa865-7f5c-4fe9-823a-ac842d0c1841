{"version": 3, "sources": ["webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/ec-canvas/ec-canvas.vue?1cb4", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/ec-canvas/ec-canvas.vue?50c8", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/ec-canvas/ec-canvas.vue?4d5e", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/ec-canvas/ec-canvas.vue?3200", "uni-app:///components/ec-canvas/ec-canvas.vue", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/ec-canvas/ec-canvas.vue?88ca", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/ec-canvas/ec-canvas.vue?1c4f"], "names": ["v1", "v2", "data", "isUseNewCanvas", "props", "canvasId", "type", "default", "ec", "forceUseOldCanvas", "mounted", "methods", "ready", "echarts", "option", "series", "console", "init", "initByOldWay", "ctx", "createCanvas", "query", "select", "boundingClientRect", "detail", "canvas", "width", "height", "canvasDpr", "exec", "initByNewWay", "fields", "node", "size", "loadImage", "image", "dpr", "canvasToTempFilePath", "opt", "uni", "touchStart", "handler", "zrX", "zrY", "preventDefault", "stopImmediatePropagation", "stopPropagation", "touchMove", "touchEnd", "created", "touch"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAu0B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC2B31B;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;EACAA;EACAC;EACA;EACA;IACAD;EACA;EACA;IACAC;EACA;EACA;IACA;IACA;IACA;MACA;IACA;MACA;IACA;EACA;EACA;AACA;AAAA,eACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;IACA;IACAG;MACAH;MACAC;IACA;EACA;EACAG;IAAA;IACA;IACA;MAAA;IAAA;EACA;EACAC;IACAC;MACA;MACA;MACAC;QACA;UACA;YACAC;cACAC;YACA;UACA;YACAD;UACA;QACA;MACA;MACA;QACAE;QACA;MACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;QACAd;MACA;MACA;QACAa;MACA;MACA;QACA;QACA;QACA;MACA;QACA;QACA;UACAA,cACA,+NACA;UACA;QACA;UACAA;UACA;QACA;MACA;IACA;IAEAE;MAAA;MACA;MACAC;MACA;MACA;QACAN;UACAO;YAAA;UAAA;QACA;MACA;QACAP;UAAA;QAAA;MACA;MACA;MACA;MACA;MACAQ,MACAC,qBACAC;QACA;UACA;QACA;UACA;QACA;UACA;YACAC;cACAC;cACAC;cACAC;cACAC;YACA;UACA;QACA;MACA,GACAC;IACA;IAEAC;MAAA;MACA;MACA;MACAT,MACAC,qBACAS;QACAC;QACAC;MACA,GACAJ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACAhB;YACAO;cAAA;YAAA;YACAc;cACA;gBACA;gBACAC;gBACAA;gBACAA;gBACA;cACA;cACAnB;cACA;YACA;UACA;QACA;UACAH;YAAA;UAAA;QACA;QACA;UACA;QACA;UACA;QACA;UACA;YACAW;cACAC;cACAC;cACAC;cACAS;YACA;UACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;QACA;QACA;QACAhB,MACAC,qBACAS;UACAC;UACAC;QACA,GACAJ;UACA;UACAS;UACAC;QACA;MACA;QACA;QACA;UACAD;QACA;QACAnB;UACAoB;QACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;QACAL;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;QACAL;MACA;IACA;IAEAM;MACA;QACA;QACA;QACAN;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;QACAL;MACA;IACA;IAEAO;MACA;QACA;QACA;QACAP;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;QACAL;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;QACAL;MACA;IACA;EACA;EACAQ;AACA;AAAA;AACA;EACA;IACA;IACAC;IACAA;EACA;EACA;AACA,C;;;;;;;;;;;;;ACpTA;AAAA;AAAA;AAAA;AAAorC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACAxsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/ec-canvas/ec-canvas.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ec-canvas.vue?vue&type=template&id=375fbf58&\"\nvar renderjs\nimport script from \"./ec-canvas.vue?vue&type=script&lang=js&\"\nexport * from \"./ec-canvas.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ec-canvas.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/ec-canvas/ec-canvas.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ec-canvas.vue?vue&type=template&id=375fbf58&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ec-canvas.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ec-canvas.vue?vue&type=script&lang=js&\"", "<template>\n    <view>\n        <!-- 新的：接口对其了H5 -->\n        <canvas\n            v-if=\"isUseNewCanvas\"\n            type=\"2d\"\n            class=\"ec-canvas\"\n            :canvas-id=\"canvasId\"\n            @init=\"init\"\n            @touchstart=\"parseEventDynamicCode($event, ec.disableTouch ? '' : 'touchStart')\"\n            @touchmove=\"parseEventDynamicCode($event, ec.disableTouch ? '' : 'touchMove')\"\n            @touchend=\"parseEventDynamicCode($event, ec.disableTouch ? '' : 'touchEnd')\"\n        ></canvas>\n        <!-- 旧的 -->\n        <canvas\n            v-else\n            class=\"ec-canvas\"\n            :canvas-id=\"canvasId\"\n            @init=\"init\"\n            @touchstart=\"parseEventDynamicCode($event, ec.disableTouch ? '' : 'touchStart')\"\n            @touchmove=\"parseEventDynamicCode($event, ec.disableTouch ? '' : 'touchMove')\"\n            @touchend=\"parseEventDynamicCode($event, ec.disableTouch ? '' : 'touchEnd')\"\n        ></canvas>\n    </view>\n</template>\n\n<script>\nimport WxCanvas from './wx-canvas';\nimport * as echarts from './echarts';\nlet ctx;\nfunction compareVersion(v1, v2) {\n    v1 = v1.split('.');\n    v2 = v2.split('.');\n    const len = Math.max(v1.length, v2.length);\n    while (v1.length < len) {\n        v1.push('0');\n    }\n    while (v2.length < len) {\n        v2.push('0');\n    }\n    for (let i = 0; i < len; i++) {\n        const num1 = parseInt(v1[i]);\n        const num2 = parseInt(v2[i]);\n        if (num1 > num2) {\n            return 1;\n        } else if (num1 < num2) {\n            return -1;\n        }\n    }\n    return 0;\n}\nexport default {\n    data() {\n        return {\n            isUseNewCanvas: false\n        };\n    },\n    props: {\n        canvasId: {\n            type: String,\n            default: 'ec-canvas'\n        },\n        ec: {\n            type: Object\n        },\n        forceUseOldCanvas: {\n            type: Boolean,\n            default: false\n        }\n    },\n    mounted() {\n        // 处理小程序 ready 生命周期\n        this.$nextTick(() => this.ready());\n    },\n    methods: {\n        ready: function () {\n            // Disable prograssive because drawImage doesn't support DOM as parameter\n            // See https://developers.weixin.qq.com/miniprogram/dev/api/canvas/CanvasContext.drawImage.html\n            echarts.registerPreprocessor((option) => {\n                if (option && option.series) {\n                    if (option.series.length > 0) {\n                        option.series.forEach((series) => {\n                            series.progressive = 0;\n                        });\n                    } else if (typeof option.series === 'object') {\n                        option.series.progressive = 0;\n                    }\n                }\n            });\n            if (!this.ec) {\n                console.warn('组件需绑定 ec 变量，例：<ec-canvas id=\"mychart-dom-bar\" canvas-id=\"mychart-bar\" ec=\"{{ ec }}\"></ec-canvas>');\n                return;\n            }\n            if (!this.ec.lazyLoad) {\n                this.init();\n            }\n        },\n\n        init: function (callback) {\n            const version = uni.getSystemInfoSync().SDKVersion;\n            const canUseNewCanvas = compareVersion(version, '2.9.0') >= 0;\n            const forceUseOldCanvas = this.forceUseOldCanvas;\n            const isUseNewCanvas = canUseNewCanvas && !forceUseOldCanvas;\n            this.setData({\n                isUseNewCanvas\n            });\n            if (forceUseOldCanvas && canUseNewCanvas) {\n                console.warn('开发者强制使用旧canvas,建议关闭');\n            }\n            if (isUseNewCanvas) {\n                // console.log('微信基础库版本大于2.9.0，开始使用<canvas type=\"2d\"/>');\n                // 2.9.0 可以使用 <canvas type=\"2d\"></canvas>\n                this.initByNewWay(callback);\n            } else {\n                const isValid = compareVersion(version, '1.9.91') >= 0;\n                if (!isValid) {\n                    console.error(\n                        '\\u5FAE\\u4FE1\\u57FA\\u7840\\u5E93\\u7248\\u672C\\u8FC7\\u4F4E\\uFF0C\\u9700\\u5927\\u4E8E\\u7B49\\u4E8E 1.9.91\\u3002\\u53C2\\u89C1\\uFF1Ahttps://github.com/ecomfe/echarts-for-weixin#%E5%BE%AE%E4%BF%A1%E7%89%88%E6%9C%AC%E8%A6%81%E6%B1%82'\n                    );\n                    return;\n                } else {\n                    console.warn('建议将微信基础库调整大于等于2.9.0版本。升级后绘图将有更好性能');\n                    this.initByOldWay(callback);\n                }\n            }\n        },\n\n        initByOldWay(callback) {\n            // 1.9.91 <= version < 2.9.0：原来的方式初始化\n            ctx = uni.createCanvasContext(this.canvasId, this);\n            const canvas = new WxCanvas(ctx, this.canvasId, false);\n            if (echarts.setPlatformAPI) {\n                echarts.setPlatformAPI({\n                    createCanvas: () => canvas\n                });\n            } else {\n                echarts.setCanvasCreator(() => canvas);\n            }\n            // const canvasDpr = wx.getSystemInfoSync().pixelRatio // 微信旧的canvas不能传入dpr\n            const canvasDpr = 1;\n            var query = uni.createSelectorQuery().in(this);\n            query\n                .select('.ec-canvas')\n                .boundingClientRect((res) => {\n                    if (typeof callback === 'function') {\n                        this.chart = callback(canvas, res.width, res.height, canvasDpr);\n                    } else if (this.ec && typeof this.ec.onInit === 'function') {\n                        this.chart = this.ec.onInit(canvas, res.width, res.height, canvasDpr);\n                    } else {\n                        this.$emit('init', {\n                            detail: {\n                                canvas: canvas,\n                                width: res.width,\n                                height: res.height,\n                                canvasDpr: canvasDpr // 增加了dpr，可方便外面echarts.init\n                            }\n                        });\n                    }\n                })\n                .exec();\n        },\n\n        initByNewWay(callback) {\n            // version >= 2.9.0：使用新的方式初始化\n            const query = uni.createSelectorQuery().in(this);\n            query\n                .select('.ec-canvas')\n                .fields({\n                    node: true,\n                    size: true\n                })\n                .exec((res) => {\n                    const canvasNode = res[0].node;\n                    this.canvasNode = canvasNode;\n                    const canvasDpr = uni.getSystemInfoSync().pixelRatio;\n                    const canvasWidth = res[0].width;\n                    const canvasHeight = res[0].height;\n                    const ctx = canvasNode.getContext('2d');\n                    const canvas = new WxCanvas(ctx, this.canvasId, true, canvasNode);\n                    if (echarts.setPlatformAPI) {\n                        echarts.setPlatformAPI({\n                            createCanvas: () => canvas,\n                            loadImage: (src, onload, onerror) => {\n                                if (canvasNode.createImage) {\n                                    const image = canvasNode.createImage();\n                                    image.onload = onload;\n                                    image.onerror = onerror;\n                                    image.src = src;\n                                    return image;\n                                }\n                                console.error('加载图片依赖 `Canvas.createImage()` API，要求小程序基础库版本在 2.7.0 及以上。');\n                                // PENDING fallback?\n                            }\n                        });\n                    } else {\n                        echarts.setCanvasCreator(() => canvas);\n                    }\n                    if (typeof callback === 'function') {\n                        this.chart = callback(canvas, canvasWidth, canvasHeight, canvasDpr);\n                    } else if (this.ec && typeof this.ec.onInit === 'function') {\n                        this.chart = this.ec.onInit(canvas, canvasWidth, canvasHeight, canvasDpr);\n                    } else {\n                        this.$emit('init', {\n                            detail: {\n                                canvas: canvas,\n                                width: canvasWidth,\n                                height: canvasHeight,\n                                dpr: canvasDpr\n                            }\n                        });\n                    }\n                });\n        },\n\n        canvasToTempFilePath(opt) {\n            if (this.isUseNewCanvas) {\n                // 新版\n                const query = uni.createSelectorQuery().in(this);\n                query\n                    .select('.ec-canvas')\n                    .fields({\n                        node: true,\n                        size: true\n                    })\n                    .exec((res) => {\n                        const canvasNode = res[0].node;\n                        opt.canvas = canvasNode;\n                        uni.canvasToTempFilePath(opt);\n                    });\n            } else {\n                // 旧的\n                if (!opt.canvasId) {\n                    opt.canvasId = this.canvasId;\n                }\n                ctx.draw(true, () => {\n                    uni.canvasToTempFilePath(opt, this);\n                });\n            }\n        },\n\n        touchStart(e) {\n            if (this.chart && e.touches.length > 0) {\n                var touch = e.touches[0];\n                var handler = this.chart.getZr().handler;\n                handler.dispatch('mousedown', {\n                    zrX: touch.x,\n                    zrY: touch.y,\n                    preventDefault: () => {},\n                    stopImmediatePropagation: () => {},\n                    stopPropagation: () => {}\n                });\n                handler.dispatch('mousemove', {\n                    zrX: touch.x,\n                    zrY: touch.y,\n                    preventDefault: () => {},\n                    stopImmediatePropagation: () => {},\n                    stopPropagation: () => {}\n                });\n                handler.processGesture(wrapTouch(e), 'start');\n            }\n        },\n\n        touchMove(e) {\n            if (this.chart && e.touches.length > 0) {\n                var touch = e.touches[0];\n                var handler = this.chart.getZr().handler;\n                handler.dispatch('mousemove', {\n                    zrX: touch.x,\n                    zrY: touch.y,\n                    preventDefault: () => {},\n                    stopImmediatePropagation: () => {},\n                    stopPropagation: () => {}\n                });\n                handler.processGesture(wrapTouch(e), 'change');\n            }\n        },\n\n        touchEnd(e) {\n            if (this.chart) {\n                const touch = e.changedTouches ? e.changedTouches[0] : {};\n                var handler = this.chart.getZr().handler;\n                handler.dispatch('mouseup', {\n                    zrX: touch.x,\n                    zrY: touch.y,\n                    preventDefault: () => {},\n                    stopImmediatePropagation: () => {},\n                    stopPropagation: () => {}\n                });\n                handler.dispatch('click', {\n                    zrX: touch.x,\n                    zrY: touch.y,\n                    preventDefault: () => {},\n                    stopImmediatePropagation: () => {},\n                    stopPropagation: () => {}\n                });\n                handler.processGesture(wrapTouch(e), 'end');\n            }\n        }\n    },\n    created: function () {}\n};\nfunction wrapTouch(event) {\n    for (let i = 0; i < event.touches.length; ++i) {\n        const touch = event.touches[i];\n        touch.offsetX = touch.x;\n        touch.offsetY = touch.y;\n    }\n    return event;\n}\n</script>\n<style>\n.ec-canvas {\n    width: 100%;\n    height: 100%;\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ec-canvas.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ec-canvas.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266621522\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}