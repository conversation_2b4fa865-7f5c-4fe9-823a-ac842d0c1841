{"version": 3, "sources": ["webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/remind/remind.vue?bdaa", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/remind/remind.vue?604d", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/remind/remind.vue?a5c1", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/remind/remind.vue?0f09", "uni-app:///components/remind/remind.vue", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/remind/remind.vue?4644", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/remind/remind.vue?8002"], "names": ["data", "props", "isFirend", "type", "default", "noShare", "methods", "shopClose", "close<PERSON><PERSON><PERSON>", "goHome", "uni", "url", "inviteNew", "created"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCkBx1B;EACAA;IACA;EACA;EACA;AACA;AACA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;AACA;AAAA,2B;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAAmiD,CAAgB,m6CAAG,EAAC,C;;;;;;;;;;;ACAvjD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/remind/remind.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./remind.vue?vue&type=template&id=7e513eb8&\"\nvar renderjs\nimport script from \"./remind.vue?vue&type=script&lang=js&\"\nexport * from \"./remind.vue?vue&type=script&lang=js&\"\nimport style0 from \"./remind.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/remind/remind.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./remind.vue?vue&type=template&id=7e513eb8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./remind.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./remind.vue?vue&type=script&lang=js&\"", "<template>\n    <view>\n        <view :class=\"isFirend ? 'remind-page common-image remind-page-firend' : 'remind-page common-image'\">\n            <view class=\"remind-text\" v-if=\"!isFirend\">今日随机对战次数已用尽。如果想要继续答题赢积分，快邀请好友对战吧</view>\n            <view class=\"remind-text\" v-else>今天你已经跟该好友对战3次，想要继续赢积分快去邀请新的好友吧！</view>\n            <view class=\"remind-btn common-image\" v-if=\"!isFirend\" @tap=\"shopClose\">邀请好友对战赢翻倍积分</view>\n            <button class=\"fx_btn_box\" :open-type=\"noShare ? '' : 'share'\" @tap=\"inviteNew\" v-else>\n                <view class=\"remind-btn2 common-image\">邀请新的好友对战赢翻倍积分</view>\n            </button>\n        </view>\n        <view v-if=\"!isFirend\" class=\"icon-close\" @tap=\"closeRemind\">\n            <image src=\"https://game.shuguos.com/upload/img/icon-close.png\" mode=\"\" />\n        </view>\n        <view v-else class=\"back-btn\" @tap=\"goHome\">返回首页</view>\n    </view>\n</template>\n\n<script>\nexport default {\n    data() {\n        return {};\n    },\n    /**\n     * 组件的属性列表\n     */\n    props: {\n        // 是否是好友对战\n        isFirend: {\n            type: Boolean,\n            default: false\n        },\n        // 是否需要分享\n        noShare: {\n            type: Boolean,\n            default: false\n        }\n    },\n    methods: {\n        shopClose() {\n            this.$emit('shopClose');\n        },\n        closeRemind() {\n            this.$emit('closeRemind');\n        },\n        goHome() {\n            uni.redirectTo({\n                url: '../home/<USER>'\n            });\n        },\n        inviteNew() {\n            this.$emit('inviteNew');\n        }\n    },\n    created: function () {}\n};\n</script>\n<style lang=\"scss\">\n@import '../../assets/css/theme.scss';\n@import '../../assets/css/common.scss';\n\n.remind-page {\n    position: relative;\n    width: 598rpx;\n    height: 669rpx;\n    padding: 147rpx 139rpx 0 99rpx;\n    margin: 0 36rpx 0 52rpx;\n    background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/info_bg.png');\n\n    .remind-text {\n        font-size: 32rpx;\n        line-height: 38rpx;\n        text-align: center;\n        color: #a56f2f;\n    }\n\n    .fx_btn_box {\n        position: absolute;\n        bottom: -62rpx;\n        left: -52rpx;\n        width: 686rpx;\n        height: 138rpx;\n        background: none;\n\n        .remind-btn2 {\n            width: 100%;\n            height: 100%;\n            padding-bottom: 12rpx;\n            background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/info_btn.png');\n\n            font-size: 48rpx;\n            font-weight: bold;\n            text-align: center;\n            line-height: 126rpx;\n            color: #fff;\n        }\n    }\n\n    .remind-btn {\n        position: absolute;\n        bottom: -62rpx;\n        left: -52rpx;\n        width: 686rpx;\n        height: 138rpx;\n        padding-bottom: 12rpx;\n        background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/info_btn.png');\n\n        font-size: 56rpx;\n        font-weight: bold;\n        text-align: center;\n        line-height: 126rpx;\n        color: #fff;\n    }\n}\n\n.remind-page-firend {\n    background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250327/count_bg.png');\n}\n\n.icon-close {\n    margin-top: 80rpx;\n    text-align: center;\n    image {\n        width: 53rpx;\n        height: 53rpx;\n        margin-top: 24rpx;\n    }\n}\n\n.back-btn {\n    padding-top: 100rpx;\n    font-size: 36rpx;\n    font-weight: bold;\n    color: #fff;\n    line-height: 50rpx;\n    text-align: center;\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./remind.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./remind.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266622781\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}