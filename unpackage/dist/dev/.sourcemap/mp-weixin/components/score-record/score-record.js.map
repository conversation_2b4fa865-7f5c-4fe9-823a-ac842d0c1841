{"version": 3, "sources": ["webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/score-record/score-record.vue?4adf", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/score-record/score-record.vue?4c35", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/score-record/score-record.vue?7d30", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/score-record/score-record.vue?2c0a", "uni-app:///components/score-record/score-record.vue", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/score-record/score-record.vue?53ac", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/score-record/score-record.vue?3e9c"], "names": ["data", "recordList", "totalScore", "loadMore", "queryParams", "user_id", "page", "limit", "props", "mounted", "methods", "attached", "getScoreList", "httpApi", "loadMoreData", "closeRecord", "detail", "created"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACc;;;AAGzE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA00B,CAAgB,0yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2B91B;AACA;AAAA,eAEA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACA;AACA;AACA;EACAC;EACAC;IACA;IACA;EACA;EACA;AACA;AACA;EACAC;IACAC;MACA;IACA;IAEAC;MAAA;MACA;QACAR,6CACA;UACAC;QAAA;MAEA;MACAQ;QACA;UACA;QACA;QACA;UACAZ;UACAC;UACAC;QACA;MACA;IACA;IAEAW;MACA;MACA,+CACA,2CACA;MACA;IACA;IAEAC;MACA;QACAC;MACA;IACA;EACA;EACAC;AACA;AAAA,2B;;;;;;;;;;;;;AC7FA;AAAA;AAAA;AAAA;AAAyiD,CAAgB,y6CAAG,EAAC,C;;;;;;;;;;;ACA7jD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/score-record/score-record.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./score-record.vue?vue&type=template&id=c0f065f8&\"\nvar renderjs\nimport script from \"./score-record.vue?vue&type=script&lang=js&\"\nexport * from \"./score-record.vue?vue&type=script&lang=js&\"\nimport style0 from \"./score-record.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/score-record/score-record.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./score-record.vue?vue&type=template&id=c0f065f8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.recordList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./score-record.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./score-record.vue?vue&type=script&lang=js&\"", "<template>\n    <view>\n        <!-- components/score-record/score-record.wxml -->\n        <view class=\"record-list common-image\">\n            <view class=\"total-score\">积分：{{ totalScore }}分</view>\n            <view class=\"record-scroll\" v-if=\"recordList.length > 0\">\n                <view class=\"record-item dis-flex-center\" v-for=\"(item, index) in recordList\" :key=\"index\">\n                    <image src=\"https://game.shuguos.com/upload/img/icon-record-rank.png\" mode=\"\" class=\"icon-record-rank\" />\n\n                    <view class=\"flex-full overFlow\">\n                        <view class=\"record-name overFlow\">{{ item.name }}</view>\n                        <view class=\"record-date\">{{ item.addtime }}</view>\n                    </view>\n\n                    <view class=\"record-score\">+{{ item.sorce }}</view>\n                </view>\n                <view class=\"load-more\" v-if=\"loadMore\" @tap=\"loadMoreData\">加载更多</view>\n            </view>\n            <view class=\"no-data-content\" v-else>暂无数据~</view>\n        </view>\n        <view class=\"icon-close\" @tap=\"closeRecord\">\n            <image src=\"https://game.shuguos.com/upload/img/icon-close.png\" mode=\"\" />\n        </view>\n    </view>\n</template>\n\n<script>\n// components/score-record/score-record.js\nconst httpApi = require('../../http/request'); //后续可以改为你自己的域名接口地址\n\nexport default {\n    data() {\n        return {\n            recordList: [],\n            totalScore: 0,\n            loadMore: false,\n            queryParams: {\n                user_id: uni.getStorageSync('userInfo').id,\n                page: 1,\n                limit: 6\n            }\n        };\n    },\n    /**\n     * 组件的属性列表\n     */\n    props: {},\n    mounted() {\n        // 处理小程序 attached 生命周期\n        this.attached();\n    },\n    /**\n     * 组件的方法列表\n     */\n    methods: {\n        attached() {\n            this.getScoreList();\n        },\n\n        getScoreList() {\n            this.setData({\n                queryParams: {\n                    ...this.queryParams,\n                    user_id: uni.getStorageSync('userInfo').id\n                }\n            });\n            httpApi.Post('/index/getScoreRecord', this.queryParams).then((res) => {\n                if (res.status) {\n                    return;\n                }\n                this.setData({\n                    recordList: [...this.recordList, ...res.data],\n                    totalScore: res.sorce,\n                    loadMore: res.data.length < this.queryParams.limit ? false : true\n                });\n            });\n        },\n\n        loadMoreData() {\n            this.queryParams.page++;\n            this.setData({\n                ['queryParams.page']: this.queryParams.page\n            });\n            this.getScoreList();\n        },\n\n        closeRecord() {\n            this.$emit('closeRecord', {\n                detail: true\n            });\n        }\n    },\n    created: function () {}\n};\n</script>\n<style lang=\"scss\">\n@import '../../assets/css/theme.scss';\n@import '../../assets/css/common.scss'; /* components/score-record/score-record.wxss */\n.record-list {\n    padding-top: 295rpx;\n    width: 622rpx;\n    height: 1054rpx;\n    box-sizing: border-box;\n    background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/score_bg.png');\n}\n.total-score {\n    margin-bottom: 35rpx;\n    font-size: 32rpx;\n    color: white;\n    text-align: center;\n}\n.record-scroll {\n    padding-left: 56rpx;\n    padding-right: 56rpx;\n    padding-bottom: 20rpx;\n    max-height: 640rpx;\n    overflow-y: scroll;\n    .record-item {\n        border-radius: 20rpx;\n        background-color: #ebf6ff;\n        color: #333;\n        padding: 26rpx 26rpx 26rpx 0;\n        margin-bottom: 10rpx;\n        .icon-record-rank {\n            width: 118rpx;\n            height: 70rpx;\n        }\n        .record-name {\n            font-size: 28rpx;\n            margin-bottom: 13rpx;\n        }\n        .record-date {\n            font-size: 24rpx;\n        }\n        .record-score {\n            color: $main-red;\n            font-size: 32rpx;\n        }\n    }\n    .load-more {\n        font-size: 28rpx;\n        color: white;\n        text-align: center;\n    }\n}\n.icon-close {\n    text-align: center;\n    image {\n        width: 53rpx;\n        height: 53rpx;\n        margin-top: 24rpx;\n    }\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./score-record.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./score-record.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266622567\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}