{"version": 3, "sources": ["webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/grade-list/grade-list.vue?56b0", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/grade-list/grade-list.vue?be6f", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/grade-list/grade-list.vue?1302", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/grade-list/grade-list.vue?9c8b", "uni-app:///components/grade-list/grade-list.vue", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/grade-list/grade-list.vue?b7ad", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/grade-list/grade-list.vue?2be9"], "names": ["data", "baseUrl", "gradeList", "selectItem", "id", "props", "mounted", "methods", "attached", "getGrade", "httpApi", "selectGrade", "console", "comfirm", "uni", "title", "icon", "duration", "Post", "user_id", "grade_id", "then", "detail", "created"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACc;;;AAGvE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAw0B,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuB51B;AACA;AACA;AAAA,eAEA;EACAA;IACA;MACA;MACAC;MACAC;MACAC;QACAC;MACA;IACA;EACA;EACA;AACA;AACA;EACAC;EACAC;IACA;IACA;EACA;EACA;AACA;AACA;EACAC;IACAC;MACA;IACA;IAEAC;MAAA;MACAC;QACA;UACA;QACA;QACA;UACAR;QACA;MACA;IACA;IAEAS;MACAC;MACA;QACAT;MACA;IACA;IAEAU;MAAA;MACA;MACA;QACAC;UACAC;UACAC;UACAC;QACA;MACA;QACAP,QACAQ;UACAC;UACAC;QACA,GACAC;UACA;UACAP;UACA;YACAQ;UACA;QACA;MACA;IACA;EACA;EACAC;AACA;AAAA,2B;;;;;;;;;;;;;ACjGA;AAAA;AAAA;AAAA;AAAuiD,CAAgB,u6CAAG,EAAC,C;;;;;;;;;;;ACA3jD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/grade-list/grade-list.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./grade-list.vue?vue&type=template&id=49412744&\"\nvar renderjs\nimport script from \"./grade-list.vue?vue&type=script&lang=js&\"\nexport * from \"./grade-list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./grade-list.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/grade-list/grade-list.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./grade-list.vue?vue&type=template&id=49412744&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./grade-list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./grade-list.vue?vue&type=script&lang=js&\"", "<template>\n    <view>\n        <!-- components/grade-list/grade-list.wxml -->\n        <view class=\"rank-page common-modal\">\n            <view class=\"grade-name\">选择年级</view>\n            <view class=\"grade-text\">比赛均根据年级匹配题库和对手</view>\n            <view class=\"grade-list dis-flex\">\n                <view\n                    :class=\"'grade-item dis-flex-center ' + (selectItem.id === item.id ? 'grade-item-active' : '')\"\n                    @tap=\"selectGrade\"\n                    :data-item=\"item\"\n                    v-for=\"(item, index) in gradeList\"\n                    :key=\"index\"\n                >\n                    {{ item.name }}\n                </view>\n            </view>\n        </view>\n        <view class=\"view-btn common-cancel-btn\" @tap=\"comfirm\">确定</view>\n    </view>\n</template>\n\n<script>\n// components/grade-list/grade-list.js\nconst setting = require('../../http/env');\nconst httpApi = require('../../http/request'); //后续可以改为你自己的域名接口地址\n\nexport default {\n    data() {\n        return {\n            //默认域名\n            baseUrl: setting.baseUrl,\n            gradeList: [],\n            selectItem: {\n                id: ''\n            }\n        };\n    },\n    /**\n     * 组件的属性列表\n     */\n    props: {},\n    mounted() {\n        // 处理小程序 attached 生命周期\n        this.attached();\n    },\n    /**\n     * 组件的方法列表\n     */\n    methods: {\n        attached() {\n            this.getGrade();\n        },\n\n        getGrade() {\n            httpApi.Post('/index/getGrade', {}).then((res) => {\n                if (res.status) {\n                    return;\n                }\n                this.setData({\n                    gradeList: res.data\n                });\n            });\n        },\n\n        selectGrade(e) {\n            console.log(e);\n            this.setData({\n                selectItem: e.currentTarget.dataset.item\n            });\n        },\n\n        comfirm() {\n            var that = this;\n            if (!this.selectItem.id) {\n                uni.showToast({\n                    title: '请先选择年级！',\n                    icon: 'none',\n                    duration: 2000\n                });\n            } else {\n                httpApi\n                    .Post('/index/upGrade', {\n                        user_id: uni.getStorageSync('userInfo').id,\n                        grade_id: this.selectItem.id\n                    })\n                    .then((res) => {\n                        var user = uni.getStorageSync('userInfo') || {};\n                        uni.setStorageSync('userInfo', Object.assign({}, user, res.data));\n                        this.$emit('closeGrade', {\n                            detail: this.selectItem.id\n                        });\n                    });\n            }\n        }\n    },\n    created: function () {}\n};\n</script>\n<style lang=\"scss\">\n@import '../../assets/css/theme.scss';\n@import '../../assets/css/common.scss'; /* components/grade-list/grade-list.wxss */\n.rank-page {\n    padding: 40rpx 0;\n    color: white;\n}\n.grade-name {\n    font-size: 40rpx;\n    text-align: center;\n    font-weight: bold;\n}\n.grade-text {\n    font-size: 26rpx;\n    margin: 10rpx auto;\n    text-align: center;\n}\n.grade-list {\n    flex-wrap: wrap;\n    justify-content: space-between;\n    padding: 20rpx 40rpx;\n    .grade-item {\n        width: 357rpx;\n        height: 70rpx;\n        color: #fff;\n        justify-content: center;\n        font-size: 28rpx;\n        border-radius: 8rpx;\n        padding: 20rpx;\n        margin-bottom: 28rpx;\n        border: 2rpx solid #fff;\n    }\n    .grade-item-active {\n        background-color: #ebf6ff;\n        color: #4aaaf8;\n        border-color: #ebf6ff;\n    }\n}\n\n.view-btn {\n    margin: 0 auto;\n    margin-top: 20rpx;\n}\n.avatar-wrapper {\n    width: 120rpx;\n    height: 120rpx;\n    overflow: hidden;\n    margin: 0 auto;\n    border-radius: 100%;\n    padding: 0;\n    margin-top: 20rpx;\n}\n.avatar {\n    width: 100%;\n    height: 100%;\n}\n.weui-input {\n    color: #fff;\n    line-height: 30px;\n    width: calc(100% - 130rpx);\n    margin: 0 auto;\n    height: 60rpx;\n    border: 4rpx solid #00a9fd;\n    padding: 0 20rpx;\n    margin-top: 30rpx;\n    margin-bottom: 10rpx;\n    border-radius: 100rpx;\n    text-align: center;\n}\n.pl {\n    color: #ccc;\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./grade-list.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./grade-list.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266622650\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}