{"version": 3, "sources": ["webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/double/double.vue?0b0b", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/double/double.vue?97f7", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/double/double.vue?f52f", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/double/double.vue?e693", "uni-app:///components/double/double.vue", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/double/double.vue?1dec", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/double/double.vue?936b"], "names": ["data", "baseUrl", "props", "num", "type", "default", "methods", "closeDb", "detail", "created"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACax1B;AACA;AAAA,eACA;EACAA;IACA;MACA;MACAC;IACA;EACA;EACA;AACA;AACA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACA;AACA;AACA;EACAC;IACAC;MACA;QACAC;MACA;IACA;EACA;EACAC;AACA;AAAA,2B;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAmiD,CAAgB,m6CAAG,EAAC,C;;;;;;;;;;;ACAvjD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/double/double.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./double.vue?vue&type=template&id=2bc0a324&\"\nvar renderjs\nimport script from \"./double.vue?vue&type=script&lang=js&\"\nexport * from \"./double.vue?vue&type=script&lang=js&\"\nimport style0 from \"./double.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/double/double.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./double.vue?vue&type=template&id=2bc0a324&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./double.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./double.vue?vue&type=script&lang=js&\"", "<template>\n    <view>\n        <!-- components/cheer/cheer.wxml -->\n        <view class=\"rank-page common-image\"></view>\n        <view class=\"view-btn common-image\">恭喜获{{ num }}倍积分</view>\n        <view class=\"icon-close\" @tap=\"closeDb\">\n            <image :src=\"baseUrl + '/upload/img/icon-close.png'\" mode=\"\" />\n        </view>\n        <view class=\"view-hei common-image\"></view>\n    </view>\n</template>\n\n<script>\n// components/cheer/cheer.js\nconst setting = require('../../http/env');\nexport default {\n    data() {\n        return {\n            //默认域名\n            baseUrl: setting.baseUrl\n        };\n    },\n    /**\n     * 组件的属性列表\n     */\n    props: {\n        num: {\n            type: Number,\n            default: 0\n        }\n    },\n    /**\n     * 组件的方法列表\n     */\n    methods: {\n        closeDb() {\n            this.$emit('closeDb', {\n                detail: true\n            });\n        }\n    },\n    created: function () {}\n};\n</script>\n<style lang=\"scss\">\n@import '../../assets/css/theme.scss';\n@import '../../assets/css/common.scss'; /* components/cheer/cheer.wxss */\n.rank-page {\n    width: 600rpx;\n    height: 600rpx;\n    box-sizing: border-box;\n    background-image: url('https://game.shuguos.com/upload/img/bi_shan.png');\n    text-align: center;\n}\n.view-btn {\n    display: flex;\n    box-sizing: border-box;\n    padding-top: 24rpx;\n    justify-content: center;\n    height: 100rpx;\n    margin: 0 auto;\n    color: white;\n    font-size: 32rpx;\n    margin-top: -150rpx;\n}\n.view-hei {\n    height: 400rpx;\n}\n.icon-close {\n    text-align: center;\n    image {\n        width: 53rpx;\n        height: 53rpx;\n        margin-top: 40rpx;\n    }\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./double.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./double.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266622832\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}