{"version": 3, "sources": ["webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/nick_img/nick_img.vue?5c17", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/nick_img/nick_img.vue?3914", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/nick_img/nick_img.vue?8faf", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/nick_img/nick_img.vue?648d", "uni-app:///components/nick_img/nick_img.vue", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/nick_img/nick_img.vue?0fc6", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/nick_img/nick_img.vue?83f9"], "names": ["data", "baseUrl", "avatarUrl", "uni", "setting", "ch_nick", "props", "methods", "bind_ch_nick", "that", "onChooseAvatar", "url", "header", "method", "filePath", "name", "formData", "onlineid", "success", "<PERSON><PERSON><PERSON>", "detail", "comfirm", "httpApi", "Post", "user_id", "nickname", "then", "created"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmB11B;AACA;AACA;AAAA,eAEA;EACAA;IACA;MACA;MACAC;MACAC,oDACAC,0DACAA,yCACAC,2DACA;MACAC;IACA;EACA;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;IACA;IACAC;MACA;MACAC;QACAJ;MACA;IACA;IACA;IACAK;MACA;MACA;MACAP;QACAQ;QACAC;UACA;QACA;QACAC;QACAC;QACAC;QACAC;UACAC;QACA;QACAC;UACA;UACA;YACA;YACA;YACAT;cACAP;YACA;UACA;QACA;MACA;IACA;IACAiB;MACA;QACAC;MACA;IACA;IACAC;MAAA;MACA;MACAC,QACAC;QACAC;QACAtB;QACAuB;MACA,GACAC;QACA;QACAvB;QACA;UACAiB;QACA;MACA;IACA;EACA;EACAO;AACA;AAAA,2B;;;;;;;;;;;;;ACrGA;AAAA;AAAA;AAAA;AAAqiD,CAAgB,q6CAAG,EAAC,C;;;;;;;;;;;ACAzjD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/nick_img/nick_img.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./nick_img.vue?vue&type=template&id=37ed2438&\"\nvar renderjs\nimport script from \"./nick_img.vue?vue&type=script&lang=js&\"\nexport * from \"./nick_img.vue?vue&type=script&lang=js&\"\nimport style0 from \"./nick_img.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/nick_img/nick_img.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./nick_img.vue?vue&type=template&id=37ed2438&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./nick_img.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./nick_img.vue?vue&type=script&lang=js&\"", "<template>\n    <view>\n        <!-- components/grade-list/grade-list.wxml -->\n        <view class=\"rank-page common-modal\">\n            <view class=\"grade-name\">选择头像昵称</view>\n            <view class=\"grade-text\">选择填写您的微信头像和微信昵称</view>\n            <button class=\"avatar-wrapper\" open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\">\n                <image class=\"avatar\" :src=\"avatarUrl\" mode=\"aspectFill\" />\n            </button>\n            <input type=\"nickname\" :value=\"ch_nick\" @change=\"bind_ch_nick\" class=\"weui-input\" placeholder-class=\"pl\" />\n        </view>\n        <view class=\"view-btn common-cancel-btn\" @tap=\"comfirm\">确定</view>\n        <view class=\"icon-close\" @tap=\"closeNick\">\n            <image :src=\"baseUrl + '/upload/img/icon-close.png'\" mode=\"\" />\n        </view>\n    </view>\n</template>\n\n<script>\n// components/grade-list/grade-list.js\nconst setting = require('../../http/env');\nconst httpApi = require('../../http/request'); //后续可以改为你自己的域名接口地址\n\nexport default {\n    data() {\n        return {\n            //默认域名\n            baseUrl: setting.baseUrl,\n            avatarUrl: uni.getStorageSync('userInfo').headimg\n                ? uni.getStorageSync('userInfo').headimg.includes('http')\n                    ? uni.getStorageSync('userInfo').headimg\n                    : setting.baseUrl + uni.getStorageSync('userInfo').headimg\n                : 'https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250327/headimg.png',\n            ch_nick: uni.getStorageSync('userInfo').nickname\n        };\n    },\n    /**\n     * 组件的属性列表\n     */\n    props: {},\n    /**\n     * 组件的方法列\n     */\n    methods: {\n        // 重新获取昵称\n        bind_ch_nick(e) {\n            var that = this;\n            that.setData({\n                ch_nick: e.detail.value\n            });\n        },\n        //重新选择头像\n        onChooseAvatar(e) {\n            var that = this;\n            const { avatarUrl } = e.detail;\n            uni.uploadFile({\n                url: setting.baseUrl + '/index/upImg',\n                header: {\n                    'content-type': 'multipart/form-data'\n                },\n                method: 'POST',\n                filePath: avatarUrl,\n                name: 'img',\n                formData: {\n                    onlineid: that.user\n                },\n                success: function (res) {\n                    //上传成功\n                    if (res.data != -1) {\n                        var img = res.data.replace(/[\\r\\n]/g, '').replace(/\\ +/g, '');\n                        var imgUrl = img.includes('http') ? img : setting.baseUrl + img;\n                        that.setData({\n                            avatarUrl: imgUrl\n                        });\n                    }\n                }\n            });\n        },\n        closeNick() {\n            this.$emit('closeNick', {\n                detail: true\n            });\n        },\n        comfirm() {\n            var that = this;\n            httpApi\n                .Post('/index/upWxNick', {\n                    user_id: uni.getStorageSync('userInfo').id,\n                    avatarUrl: that.avatarUrl,\n                    nickname: that.ch_nick\n                })\n                .then((res) => {\n                    var user = uni.getStorageSync('userInfo') || {};\n                    uni.setStorageSync('userInfo', Object.assign({}, user, res.data));\n                    this.$emit('closeNick', {\n                        detail: true\n                    });\n                });\n        }\n    },\n    created: function () {}\n};\n</script>\n<style lang=\"scss\">\n@import '../../assets/css/theme.scss';\n@import '../../assets/css/common.scss'; /* components/grade-list/grade-list.wxss */\n.rank-page {\n    width: 600rpx;\n    padding: 40rpx 0;\n    color: white;\n    padding-bottom: 60rpx;\n}\n.grade-name {\n    font-size: 40rpx;\n    text-align: center;\n    font-weight: bold;\n}\n.grade-text {\n    font-size: 26rpx;\n    margin: 10rpx auto;\n    text-align: center;\n}\n.grade-list {\n    flex-wrap: wrap;\n    justify-content: flex-start; /* 替代原先的space-between布局方式*/\n    padding: 20rpx 40rpx;\n    .grade-item {\n        width: 200rpx;\n        height: 70rpx;\n        background-color: #00a9fd;\n        color: white;\n        justify-content: center;\n        border: solid 4rpx transparent;\n        font-size: 36rpx;\n        border-radius: 10rpx;\n        padding: 20rpx;\n        margin-bottom: 20rpx;\n        &:nth-child(2n + 1) {\n            margin-right: 20rpx;\n        }\n    }\n    .grade-item-active {\n        background-color: #ffe314;\n        color: #e89912;\n    }\n}\n\n.view-btn {\n    margin: 0 auto;\n    margin-top: 20rpx;\n}\n.avatar-wrapper {\n    width: 120rpx;\n    height: 120rpx;\n    overflow: hidden;\n    margin: 0 auto;\n    border-radius: 100%;\n    padding: 0;\n    margin-top: 20rpx;\n}\n.avatar {\n    width: 100%;\n    height: 100%;\n}\n.weui-input {\n    color: #333;\n    line-height: 30px;\n    width: calc(100% - 130rpx);\n    margin: 0 auto;\n    height: 60rpx;\n    background: #ebf6ff;\n    padding: 10rpx 20rpx;\n    margin-top: 30rpx;\n    margin-bottom: 10rpx;\n    border-radius: 100rpx;\n    text-align: center;\n}\n.pl {\n    color: #ccc;\n}\n.icon-close {\n    text-align: center;\n    image {\n        width: 53rpx;\n        height: 53rpx;\n        margin-top: 24rpx;\n    }\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./nick_img.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./nick_img.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266622748\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}