{"version": 3, "sources": ["webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/prize/prize.vue?bfb5", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/prize/prize.vue?4c13", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/prize/prize.vue?3414", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/prize/prize.vue?4e12", "uni-app:///components/prize/prize.vue", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/prize/prize.vue?920e", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/prize/prize.vue?c5ef"], "names": ["data", "baseUrl", "props", "prizeList", "type", "default", "methods", "closePrize", "detail", "viewPrize", "uni", "url", "created"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyBv1B;AACA;AAAA,gBACA;EACAA;IACA;MACA;MACAC;IACA;EACA;EACA;AACA;AACA;EACAC;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;EACA;EACA;AACA;AACA;EACAC;IACAC;MACA;QACAC;MACA;IACA;IACAC;MACAC;QACAC;MACA;MACA;IACA;EACA;EACAC;AACA;AAAA,4B;;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAAkiD,CAAgB,k6CAAG,EAAC,C;;;;;;;;;;;ACAtjD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/prize/prize.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./prize.vue?vue&type=template&id=d465612c&\"\nvar renderjs\nimport script from \"./prize.vue?vue&type=script&lang=js&\"\nexport * from \"./prize.vue?vue&type=script&lang=js&\"\nimport style0 from \"./prize.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/prize/prize.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prize.vue?vue&type=template&id=d465612c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prize.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prize.vue?vue&type=script&lang=js&\"", "<template>\n    <view>\n        <!-- components/prize.wxml -->\n        <view class=\"prize-dialog\">\n            <image src=\"https://game.shuguos.com/upload/img/prize-head.png\" mode=\"\" class=\"prize-head\" />\n        </view>\n        <view class=\"record-list common-modal\">\n            <swiper class=\"swiper-list\" :indicator-dots=\"true\">\n                <swiper-item v-for=\"(item, index) in prizeList\" :key=\"index\">\n                    <view class=\"prize-text\">恭喜你获得</view>\n\n                    <image :src=\"baseUrl + item.img\" mode=\"aspectFit\" class=\"prize-example\" />\n\n                    <view class=\"prize-introduce\">{{ item.name }}</view>\n                </swiper-item>\n            </swiper>\n        </view>\n        <view class=\"view-btn common-cancel-btn\" @tap=\"viewPrize\">查看</view>\n        <view class=\"icon-close\" @tap=\"closePrize\">\n            <image src=\"https://game.shuguos.com/upload/img/icon-close.png\" mode=\"\" />\n        </view>\n    </view>\n</template>\n\n<script>\n// components/prize.js\nconst setting = require('../../http/env');\nexport default {\n    data() {\n        return {\n            //默认域名\n            baseUrl: setting.baseUrl\n        };\n    },\n    /**\n     * 组件的属性列表\n     */\n    props: {\n        prizeList: {\n            type: Array,\n            default: () => []\n        }\n    },\n    /**\n     * 组件的方法列表\n     */\n    methods: {\n        closePrize() {\n            this.$emit('closePrize', {\n                detail: true\n            });\n        },\n        viewPrize() {\n            uni.navigateTo({\n                url: '/pages/prize-list/prize-list'\n            });\n            this.closePrize();\n        }\n    },\n    created: function () {}\n};\n</script>\n<style lang=\"scss\">\n@import '../../assets/css/theme.scss';\n@import '../../assets/css/common.scss'; /* components/prize.wxss */\n.prize-dialog {\n    text-align: center;\n    .prize-head {\n        width: 343rpx;\n        height: 260rpx;\n    }\n}\n.record-list {\n    width: 622rpx;\n    height: 626rpx;\n    margin-top: -148rpx;\n    padding-top: 148rpx;\n    text-align: center;\n\n    .prize-text {\n        font-size: 36rpx;\n        color: white;\n        margin-top: 50rpx;\n    }\n    .prize-example {\n        width: 160rpx;\n        height: 160rpx;\n        margin-top: 58rpx;\n        margin-bottom: 22rpx;\n    }\n    .prize-introduce {\n        font-size: 28rpx;\n        color: white;\n    }\n}\n.swiper-list {\n    height: 440rpx;\n}\n.view-btn {\n    margin: 0 auto;\n    margin-top: -50rpx;\n}\n.icon-close {\n    text-align: center;\n    image {\n        width: 53rpx;\n        height: 53rpx;\n        margin-top: 24rpx;\n    }\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prize.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prize.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266622615\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}