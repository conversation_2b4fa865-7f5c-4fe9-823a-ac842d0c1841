{"version": 3, "sources": ["webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/user-info/user-info.vue?3017", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/user-info/user-info.vue?f070", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/user-info/user-info.vue?6a18", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/user-info/user-info.vue?afdc", "uni-app:///components/user-info/user-info.vue", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/user-info/user-info.vue?dcd8", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/user-info/user-info.vue?08fb"], "names": ["data", "baseUrl", "imgUrl", "props", "userInfo", "type", "default", "methods", "handlePageShow", "openRecord", "detail", "openNick", "watch", "created"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAu0B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACe31B;AACA;AACA;AAAA,gBAEA;EACAA;IACA;MACAC;MACAC;MACA;IACA;EACA;;EAEA;AACA;AACA;EACAC;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACAC;MAAA;MACA;QACA;MACA;MACA;QACAN;MACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACAO;MACA;QACAC;MACA;IACA;IAEA;IACAC;MACA;QACAD;MACA;IACA;EACA;EAEAE;IACAR;MACA;QACA;UACAF;QACA;MACA;IACA;EACA;EAEAW;AACA;AAAA,4B;;;;;;;;;;;;ACzFA;AAAA;AAAA;AAAA;AAAsiD,CAAgB,s6CAAG,EAAC,C;;;;;;;;;;;ACA1jD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/user-info/user-info.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./user-info.vue?vue&type=template&id=ba61afb4&\"\nvar renderjs\nimport script from \"./user-info.vue?vue&type=script&lang=js&\"\nexport * from \"./user-info.vue?vue&type=script&lang=js&\"\nimport style0 from \"./user-info.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/user-info/user-info.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user-info.vue?vue&type=template&id=ba61afb4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user-info.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user-info.vue?vue&type=script&lang=js&\"", "<template>\n    <!-- components/user-info/user-info.wxml -->\n    <view class=\"user-info dis-flex-center\">\n        <view class=\"wechat-view common-image\" />\n        <view class=\"user-image\" @tap=\"openNick\">\n            <image :src=\"imgUrl || 'https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250327/headimg.png'\" mode=\"aspectFill\" />\n        </view>\n        <view class=\"user-score dis-flex-center\" @tap=\"openRecord\">\n            <image :src=\"baseUrl + '/upload/img/icon-score.png'\" mode=\"\" />\n            <text>{{ userInfo.sorce || 0 }}</text>\n        </view>\n    </view>\n</template>\n\n<script>\n// components/user-info/user-info.js\nconst config = require('../../http/env');\nconst httpApi = require('../../http/request'); //后续可以改为你自己的域名接口地址\n\nexport default {\n    data() {\n        return {\n            baseUrl: config.baseUrl,\n            imgUrl: ''\n            // userInfo: {},\n        };\n    },\n\n    /**\n     * 组件的属性列表\n     */\n    props: {\n        userInfo: {\n            type: Object,\n            default: () => ({})\n        }\n    },\n\n    /**\n     * 组件的方法列表\n     */\n    methods: {\n        handlePageShow: function () {\n            if (!this.userInfo?.headimg) {\n                return;\n            }\n            this.setData({\n                imgUrl: this.userInfo.headimg.includes('http') ? this.userInfo.headimg : config.baseUrl + this.userInfo.headimg\n            });\n        },\n\n        // 获取用户信息\n        // getUserInfoByApi() {\n        //   var that = this;\n        //   var user = wx.getStorageSync(\"userInfo\");\n        //   httpApi.Post(\"/index/getUser\", { user_id: user.id }).then((res) => {\n        //     wx.setStorageSync(\"userInfo\", res.data);\n        //     that.setData({\n        //       userInfo: res.data,\n        //     });\n        //   });\n        // },\n\n        // 打开积分记录\n        openRecord() {\n            this.$emit('openRecord', {\n                detail: true\n            });\n        },\n\n        // 打开头像昵称\n        openNick() {\n            this.$emit('openNick', {\n                detail: true\n            });\n        }\n    },\n\n    watch: {\n        userInfo: function (val) {\n            if (val && val.headimg) {\n                this.setData({\n                    imgUrl: val.headimg.includes('http') ? val.headimg : config.baseUrl + val.headimg\n                });\n            }\n        }\n    },\n\n    created: function () {}\n};\n</script>\n<style lang=\"scss\">\n@import '../../assets/css/theme.scss';\n@import '../../assets/css/common.scss'; /* components/user-info/user-info.wxss */\n/* 用户信息+分数*/\n.user-info {\n    position: relative;\n    margin: 0 16rpx;\n    .user-image {\n        width: 90rpx;\n        height: 90rpx;\n        border-radius: 50%;\n        margin-right: 24rpx;\n        background: #d9d9d9;\n        image {\n            width: 100%;\n            height: 100%;\n            border-radius: 50%;\n        }\n    }\n    .user-score {\n        border: solid 2rpx $deep-blue;\n        border-radius: 56rpx;\n        background: #fff;\n        position: relative;\n        padding: 6rpx 50rpx 6rpx 66rpx;\n        color: $deep-blue;\n        image {\n            position: absolute;\n            left: -4rpx;\n            width: 60rpx;\n            height: 60rpx;\n            margin-right: 16rpx;\n        }\n        text {\n            font-size: 32rpx;\n            font-weight: bold;\n        }\n    }\n    .wechat-view {\n        width: 201rpx;\n        height: 68rpx;\n        position: absolute;\n        z-index: 99;\n        top: 70rpx;\n        left: 40rpx;\n        background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/common_info.png');\n    }\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user-info.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user-info.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266622549\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}