{"version": 3, "sources": ["webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/cheer/cheer.vue?a574", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/cheer/cheer.vue?fe25", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/cheer/cheer.vue?13fe", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/cheer/cheer.vue?d60e", "uni-app:///components/cheer/cheer.vue", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/cheer/cheer.vue?2ed1", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/cheer/cheer.vue?c3d9"], "names": ["data", "dataNum", "props", "tis", "type", "default", "showContinue", "mounted", "methods", "attached", "exit", "uni", "url", "closeChe", "detail", "randomNum", "console", "created"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgBv1B;AAAA,eACA;EACAA;IACA;MACAC;IACA;EACA;EACA;AACA;AACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;IACA;EACA;EACA;AACA;AACA;EACAC;IACAC;MACA;IACA;IAEAC;MACA;MACAC;QACAC;MACA;IACA;IAEAC;MACA;QACAC;MACA;IACA;IAEAC;MACA;MACAC;MACA;QACAf;MACA;IACA;EACA;EACAgB;AACA;AAAA,2B;;;;;;;;;;;;;ACtEA;AAAA;AAAA;AAAA;AAAkiD,CAAgB,k6CAAG,EAAC,C;;;;;;;;;;;ACAtjD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/cheer/cheer.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./cheer.vue?vue&type=template&id=4bfee2c0&\"\nvar renderjs\nimport script from \"./cheer.vue?vue&type=script&lang=js&\"\nexport * from \"./cheer.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cheer.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/cheer/cheer.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cheer.vue?vue&type=template&id=4bfee2c0&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cheer.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cheer.vue?vue&type=script&lang=js&\"", "<template>\n    <view>\n        <!-- components/cheer/cheer.wxml -->\n        <view class=\"rank-page common-image\">\n            <view class=\"warn-text\">加油！</view>\n            <view class=\"warn-text\">{{ tis }}</view>\n        </view>\n        <view class=\"dis-flex-center btn-view\">\n            <view class=\"btn common-cancel-btn dis-flex\" @tap=\"exit\">继续返回</view>\n            <view v-if=\"showContinue\" class=\"btn common-confirm-btn dis-flex\" @tap=\"closeChe\">继续答题</view>\n            <view v-else class=\"btn dis-flex common-btn-no\">继续答题</view>\n        </view>\n    </view>\n</template>\n\n<script>\n// components/cheer/cheer.js\nexport default {\n    data() {\n        return {\n            dataNum: 2\n        };\n    },\n    /**\n     * 组件的属性列表\n     */\n    props: {\n        tis: {\n            type: String,\n            default: ''\n        },\n        showContinue: {\n            type: Boolean,\n            default: true\n        }\n    },\n    mounted() {\n        // 处理小程序 attached 生命周期\n        this.attached();\n    },\n    /**\n     * 组件的方法列表\n     */\n    methods: {\n        attached() {\n            this.randomNum();\n        },\n\n        exit(e) {\n            var that = this;\n            uni.redirectTo({\n                url: '../home/<USER>'\n            });\n        },\n\n        closeChe() {\n            this.$emit('closeChe', {\n                detail: true\n            });\n        },\n\n        randomNum() {\n            const randomVal = Math.floor(Math.random() * 4) + 2;\n            console.log('randomNum', randomVal);\n            this.setData({\n                dataNum: randomVal\n            });\n        }\n    },\n    created: function () {}\n};\n</script>\n<style lang=\"scss\">\n@import '../../assets/css/theme.scss';\n@import '../../assets/css/common.scss'; /* components/cheer/cheer.wxss */\n.rank-page {\n    padding-top: 200rpx;\n    width: 500rpx;\n    height: 394rpx;\n    box-sizing: border-box;\n    background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/modal_bg.png');\n    text-align: center;\n}\n\n.warn-text {\n    font-size: 32rpx;\n    color: white;\n    line-height: 1.5;\n    width: 90%;\n    margin: 0 auto;\n    font-weight: bold;\n}\n.btn-view {\n    margin-top: 50rpx;\n    padding: 0;\n    justify-content: space-between;\n\n    .btn {\n        padding: 0 40rpx;\n    }\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cheer.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cheer.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266622817\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}