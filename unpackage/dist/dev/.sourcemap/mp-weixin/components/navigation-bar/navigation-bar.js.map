{"version": 3, "sources": ["webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/navigation-bar/navigation-bar.vue?0db3", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/navigation-bar/navigation-bar.vue?cc43", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/navigation-bar/navigation-bar.vue?070b", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/navigation-bar/navigation-bar.vue?a844", "uni-app:///components/navigation-bar/navigation-bar.vue", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/navigation-bar/navigation-bar.vue?199d", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/navigation-bar/navigation-bar.vue?cb03"], "names": ["data", "displayStyle", "ios", "innerPaddingRight", "leftWidth", "safeAreaTop", "options", "multipleSlots", "props", "extClass", "type", "default", "title", "background", "color", "back", "loading", "homeButton", "animated", "show", "delta", "mounted", "methods", "attached", "uni", "success", "showChangeFun", "backFun", "detail", "home", "console", "created", "watch", "handler", "immediate"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACc;;;AAG3E;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA40B,CAAgB,4yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2Dh2B;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;EACA;;EACA;AACA;AACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACA;MACAR;MACAC;IACA;IACAQ;MACA;MACAT;MAEAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;EACA;EACAU;IACA;IACA;EACA;EACA;AACA;AACA;EACAC;IACAC;MAAA;MACA;MACAC;QACAC;UACA;UACA;UACA;UACA;YACAvB;YACAC;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEAqB;MACA;MACA;MACA;QACAzB;MACA;QACAA;MACA;MACA;QACAA;MACA;IACA;IAEA0B;MACA;MACA;QACAH;UACAJ;QACA;MACA;MACA,WACA,QACA;QACAQ;UACAR;QACA;MACA,GACA,GACA;IACA;IAEAS;MACAC;IACA;EACA;EACAC;EACAC;IACAb;MACAc;QACA;QACA;QACA;UACAhC;QACA;UACAA;QACA;QACA;UACAA;QACA;MACA;MAEAiC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxMA;AAAA;AAAA;AAAA;AAA2iD,CAAgB,26CAAG,EAAC,C;;;;;;;;;;;ACA/jD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/navigation-bar/navigation-bar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./navigation-bar.vue?vue&type=template&id=401b6804&\"\nvar renderjs\nimport script from \"./navigation-bar.vue?vue&type=script&lang=js&\"\nexport * from \"./navigation-bar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./navigation-bar.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/navigation-bar/navigation-bar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./navigation-bar.vue?vue&type=template&id=401b6804&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./navigation-bar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./navigation-bar.vue?vue&type=script&lang=js&\"", "<template>\n    <view :class=\"'weui-navigation-bar ' + extClass\">\n        <view\n            :class=\"'weui-navigation-bar__inner ' + (ios ? 'ios' : 'android')\"\n            :style=\"'color: ' + color + '; background: ' + background + '; ' + displayStyle + '; ' + innerPaddingRight + '; ' + safeAreaTop\"\n        >\n            <!-- 左侧按钮 -->\n            <view class=\"weui-navigation-bar__left\" :style=\"leftWidth\">\n                <block v-if=\"back || homeButton\">\n                    <!-- 返回上一页 -->\n                    <block v-if=\"back\">\n                        <view class=\"weui-navigation-bar__buttons weui-navigation-bar__buttons_goback\">\n                            <view\n                                @tap=\"backFun\"\n                                class=\"weui-navigation-bar__btn_goback_wrapper\"\n                                hover-class=\"weui-active\"\n                                hover-stay-time=\"100\"\n                                aria-role=\"button\"\n                                aria-label=\"返回\"\n                            >\n                                <image src=\"https://game.shuguos.com/upload/img/icon-back.png\" mode=\"\" class=\"icon-back\" />\n                                <!-- <view class=\"weui-navigation-bar__button weui-navigation-bar__btn_goback\"></view> -->\n                            </view>\n                        </view>\n                    </block>\n                    <!-- 返回首页 -->\n                    <block v-if=\"homeButton\">\n                        <view class=\"weui-navigation-bar__buttons weui-navigation-bar__buttons_home\">\n                            <view @tap=\"home\" class=\"weui-navigation-bar__btn_home_wrapper\" hover-class=\"weui-active\" aria-role=\"button\" aria-label=\"首页\">\n                                <view class=\"weui-navigation-bar__button weui-navigation-bar__btn_home\" />\n                            </view>\n                        </view>\n                    </block>\n                </block>\n                <block v-else>\n                    <slot name=\"left\" />\n                </block>\n            </view>\n            <!-- 标题 -->\n            <view class=\"weui-navigation-bar__center\">\n                <view v-if=\"loading\" class=\"weui-navigation-bar__loading\" aria-role=\"alert\">\n                    <view class=\"weui-loading\" aria-role=\"img\" aria-label=\"加载中\" />\n                </view>\n                <block v-if=\"title\">\n                    <text>{{ title }}</text>\n                </block>\n                <block v-else>\n                    <slot name=\"center\" />\n                </block>\n            </view>\n            <!-- 右侧留空 -->\n            <view class=\"weui-navigation-bar__right\">\n                <slot name=\"right\" />\n            </view>\n        </view>\n    </view>\n</template>\n\n<script>\nexport default {\n    data() {\n        return {\n            displayStyle: '',\n            ios: '',\n            innerPaddingRight: '',\n            leftWidth: '',\n            safeAreaTop: ''\n        };\n    },\n    options: {\n        multipleSlots: true // 在组件定义时的选项中启用多slot支持\n    },\n    /**\n     * 组件的属性列表\n     */\n    props: {\n        extClass: {\n            type: String,\n            default: ''\n        },\n        title: {\n            type: String,\n            default: ''\n        },\n        background: {\n            type: String,\n            default: ''\n        },\n        color: {\n            type: String,\n            default: ''\n        },\n        back: {\n            type: Boolean,\n            default: true\n        },\n        loading: {\n            type: Boolean,\n            default: false\n        },\n        homeButton: {\n            type: Boolean,\n            default: false\n        },\n        animated: {\n            // 显示隐藏的时候opacity动画效果\n            type: Boolean,\n            default: true\n        },\n        show: {\n            // 显示隐藏导航，隐藏的时候navigation-bar的高度占位还在\n            type: Boolean,\n\n            default: true\n        },\n        // back为true的时候，返回的页面深度\n        delta: {\n            type: Number,\n            default: 1\n        }\n    },\n    mounted() {\n        // 处理小程序 attached 生命周期\n        this.attached();\n    },\n    /**\n     * 组件的方法列表\n     */\n    methods: {\n        attached() {\n            const rect = uni.getMenuButtonBoundingClientRect();\n            uni.getSystemInfo({\n                success: (res) => {\n                    const isAndroid = res.platform === 'android';\n                    const isDevtools = res.platform === 'devtools';\n                    const top = res.safeArea.top || 30;\n                    this.setData({\n                        ios: !isAndroid,\n                        innerPaddingRight: `padding-right: ${res.windowWidth - rect.left}px`,\n                        leftWidth: `width: ${res.windowWidth - rect.left}px`,\n                        safeAreaTop: isDevtools || isAndroid ? `height: calc(var(--height) + ${top}px); padding-top: ${top}px` : ``\n                    });\n                }\n            });\n        },\n\n        showChangeFun(show) {\n            const animated = this.animated;\n            let displayStyle = '';\n            if (animated) {\n                displayStyle = `opacity: ${show ? '1' : '0'};transition:opacity 0.5s;`;\n            } else {\n                displayStyle = `display: ${show ? '' : 'none'}`;\n            }\n            this.setData({\n                displayStyle\n            });\n        },\n\n        backFun() {\n            const data = this;\n            if (data.delta) {\n                uni.navigateBack({\n                    delta: data.delta\n                });\n            }\n            this.$emit(\n                'back',\n                {\n                    detail: {\n                        delta: data.delta\n                    }\n                },\n                {}\n            );\n        },\n\n        home() {\n            console.log('占位：函数 home 未声明');\n        }\n    },\n    created: function () {},\n    watch: {\n        show: {\n            handler: function (show) {\n                const animated = this.animated;\n                let displayStyle = '';\n                if (animated) {\n                    displayStyle = `opacity: ${show ? '1' : '0'};transition:opacity 0.5s;`;\n                } else {\n                    displayStyle = `display: ${show ? '' : 'none'}`;\n                }\n                this.setData({\n                    displayStyle\n                });\n            },\n\n            immediate: true\n        }\n    }\n};\n</script>\n<style lang=\"scss\">\n.weui-navigation-bar {\n    --weui-FG-0: rgba(0, 0, 0, 0.9);\n    --height: 44px;\n    --left: 16px;\n}\n.weui-navigation-bar .android {\n    --height: 48px;\n}\n\n.weui-navigation-bar {\n    overflow: hidden;\n    color: var(--weui-FG-0);\n    flex: none;\n}\n\n.weui-navigation-bar__inner {\n    position: relative;\n    top: 0;\n    left: 0;\n    height: calc(var(--height) + env(safe-area-inset-top));\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    justify-content: center;\n    padding-top: env(safe-area-inset-top);\n    width: 100%;\n    box-sizing: border-box;\n}\n\n.weui-navigation-bar__left {\n    position: relative;\n    padding-left: var(--left);\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    height: 100%;\n    box-sizing: border-box;\n}\n\n.weui-navigation-bar__btn_goback_wrapper {\n    padding: 11px 18px 11px 16px;\n    margin: 0 -18px -11px -16px;\n}\n\n.weui-navigation-bar__btn_goback_wrapper.weui-active {\n    opacity: 0.5;\n}\n\n.weui-navigation-bar__btn_goback {\n    font-size: 12px;\n    width: 12px;\n    height: 24px;\n    -webkit-mask: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='24' viewBox='0 0 12 24'%3E  %3Cpath fill-opacity='.9' fill-rule='evenodd' d='M10 19.438L8.955 20.5l-7.666-7.79a1.02 1.02 0 0 1 0-1.42L8.955 3.5 10 4.563 2.682 12 10 19.438z'/%3E%3C/svg%3E\")\n        no-repeat 50% 50%;\n    mask: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='24' viewBox='0 0 12 24'%3E  %3Cpath fill-opacity='.9' fill-rule='evenodd' d='M10 19.438L8.955 20.5l-7.666-7.79a1.02 1.02 0 0 1 0-1.42L8.955 3.5 10 4.563 2.682 12 10 19.438z'/%3E%3C/svg%3E\")\n        no-repeat 50% 50%;\n    -webkit-mask-size: cover;\n    mask-size: cover;\n    background-color: var(--weui-FG-0);\n}\n\n.weui-navigation-bar__center {\n    font-size: 32rpx;\n    text-align: center;\n    position: relative;\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    justify-content: center;\n    font-weight: bold;\n    flex: 1;\n    height: 100%;\n}\n\n.weui-navigation-bar__loading {\n    margin-right: 4px;\n    align-items: center;\n}\n\n.weui-loading {\n    font-size: 16px;\n    width: 16px;\n    height: 16px;\n    display: block;\n    background: transparent\n        url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='80px' height='80px' viewBox='0 0 80 80' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Eloading%3C/title%3E%3Cdefs%3E%3ClinearGradient x1='94.0869141%25' y1='0%25' x2='94.0869141%25' y2='90.559082%25' id='linearGradient-1'%3E%3Cstop stop-color='%23606060' stop-opacity='0' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23606060' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.67370605%25' x2='100%25' y2='90.6286621%25' id='linearGradient-2'%3E%3Cstop stop-color='%23606060' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23606060' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3C/defs%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' opacity='0.9'%3E%3Cg%3E%3Cpath d='M40,0 C62.09139,0 80,17.90861 80,40 C80,62.09139 62.09139,80 40,80 L40,73 C58.2253967,73 73,58.2253967 73,40 C73,21.7746033 58.2253967,7 40,7 L40,0 Z' fill='url(%23linearGradient-1)'%3E%3C/path%3E%3Cpath d='M40,0 L40,7 C21.7746033,7 7,21.7746033 7,40 C7,58.2253967 21.7746033,73 40,73 L40,80 C17.90861,80 0,62.09139 0,40 C0,17.90861 17.90861,0 40,0 Z' fill='url(%23linearGradient-2)'%3E%3C/path%3E%3Ccircle id='Oval' fill='%23606060' cx='40.5' cy='3.5' r='3.5'%3E%3C/circle%3E%3C/g%3E%3C/g%3E%3C/svg%3E%0A\")\n        no-repeat;\n    background-size: 100%;\n    margin-left: 0;\n    animation: loading linear infinite 1s;\n}\n\n@keyframes loading {\n    from {\n        transform: rotate(0);\n    }\n    to {\n        transform: rotate(360deg);\n    }\n}\n\n.icon-back {\n    width: 63rpx;\n    height: 63rpx;\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./navigation-bar.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./navigation-bar.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266622859\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}