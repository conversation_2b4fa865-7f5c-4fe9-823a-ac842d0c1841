{"version": 3, "sources": ["webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/exit-warn/exit-warn.vue?dc0b", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/exit-warn/exit-warn.vue?1a74", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/exit-warn/exit-warn.vue?be1e", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/exit-warn/exit-warn.vue?8062", "uni-app:///components/exit-warn/exit-warn.vue", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/exit-warn/exit-warn.vue?daad", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/components/exit-warn/exit-warn.vue?8a98"], "names": ["data", "props", "methods", "closeExit", "detail", "exit", "uni", "url", "created"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAu0B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmB31B;AAAA,eACA;EACAA;IACA;EACA;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;IACAC;MACA;QACAC;MACA;IACA;IACAC;MACAC;QACAC;MACA;IACA;EACA;EACAC;AACA;AAAA,2B;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAAsiD,CAAgB,s6CAAG,EAAC,C;;;;;;;;;;;ACA1jD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/exit-warn/exit-warn.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./exit-warn.vue?vue&type=template&id=0fc7d1b0&\"\nvar renderjs\nimport script from \"./exit-warn.vue?vue&type=script&lang=js&\"\nexport * from \"./exit-warn.vue?vue&type=script&lang=js&\"\nimport style0 from \"./exit-warn.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/exit-warn/exit-warn.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exit-warn.vue?vue&type=template&id=0fc7d1b0&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exit-warn.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exit-warn.vue?vue&type=script&lang=js&\"", "<template>\n    <view>\n        <!-- components/exit-warn/exit-warn.wxml -->\n        <view class=\"rank-page common-modal\">\n            <view class=\"warn-text\">确认离开嘛？</view>\n            <view class=\"warn-text text-sm\">好孩子是不会轻言放弃的哦~</view>\n            <image src=\"https://game.shuguos.com/upload/img/icon-fail.png\" mode=\"\" class=\"icon-fail\" />\n        </view>\n        <view class=\"dis-flex-center btn-view\">\n            <view class=\"btn common-cancel-btn dis-flex\" @tap=\"exit\">去意已决</view>\n            <view class=\"btn common-confirm-btn dis-flex\" @tap=\"closeExit\">继续PK</view>\n        </view>\n        <view class=\"icon-close\" @tap=\"closeExit\">\n            <image src=\"https://game.shuguos.com/upload/img/icon-close.png\" mode=\"\" />\n        </view>\n    </view>\n</template>\n\n<script>\n// components/exit-warn/exit-warn.js\nexport default {\n    data() {\n        return {};\n    },\n    /**\n     * 组件的属性列表\n     */\n    props: {},\n    /**\n     * 组件的方法列表\n     */\n    methods: {\n        closeExit() {\n            this.$emit('closeExitWarn', {\n                detail: true\n            });\n        },\n        exit() {\n            uni.redirectTo({\n                url: '../home/<USER>'\n            });\n        }\n    },\n    created: function () {}\n};\n</script>\n<style lang=\"scss\">\n@import '../../assets/css/theme.scss';\n@import '../../assets/css/common.scss'; /* components/exit-warn/exit-warn.wxss */\n.rank-page {\n    width: 520rpx;\n    padding-top: 80rpx;\n    height: 408rpx;\n    box-sizing: border-box;\n    text-align: center;\n    .icon-fail {\n        width: 128rpx;\n        height: 128rpx;\n        margin: 24rpx auto;\n    }\n}\n\n.warn-text {\n    font-size: 36rpx;\n    color: white;\n    line-height: 1.5;\n}\n.text-sm {\n    font-size: 28rpx;\n}\n.btn-view {\n    margin-top: 50rpx;\n    padding: 0 40rpx;\n    justify-content: space-between;\n\n    .btn {\n        padding: 0 40rpx;\n    }\n}\n\n.icon-close {\n    text-align: center;\n    image {\n        width: 53rpx;\n        height: 53rpx;\n        margin-top: 24rpx;\n    }\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exit-warn.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exit-warn.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266622805\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}