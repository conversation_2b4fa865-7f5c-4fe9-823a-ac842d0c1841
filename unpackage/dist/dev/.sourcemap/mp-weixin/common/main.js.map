{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/App.vue?e02e", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/App.vue?4a44", "uni-app:///App.vue", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/App.vue?475d", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/App.vue?7fe1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "use", "zpMixins", "config", "productionTip", "App", "mpType", "app", "$mount", "data", "onLaunch", "logs", "uni", "updateManager", "console", "title", "content", "success", "globalData", "userInfo", "shareInfo", "button", "imageUrl", "menu"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAG3D;AAGA;AAAsB;AAAA;AAPtB;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAQ1DC,YAAG,CAACC,GAAG,CAACC,cAAQ,CAAC;AAEjBF,YAAG,CAACG,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAClB,IAAMC,GAAG,GAAG,IAAIP,YAAG,mBACZK,YAAG,EACR;AACF,UAAAE,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;ACjBZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACa;;;AAG/D;AAC0M;AAC1M,gBAAgB,iNAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAmyB,CAAgB,iyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACCvzB;AAAA,eACA;EACAC;IACA;EACA;EACAC;IACA;IACA;IACAC;IACAC;;IAEA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;MACA;MACAC;QACAC;QACA;QACA;UACAA;UACAD;YACAD;cACAG;cACAC;cACAC;gBACAH;gBACA;gBACA;kBACA;kBACAD;gBACA;cACA;YACA;UACA;UACAA;YACA;YACAD;cACAG;cACAC;YACA;UACA;QACA;MACA;IACA;EACA;EACAE;IACAC;IACAC;MACAC;QACAN;QACAO;MACA;MACAC;QACAR;QACAO;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjEA;AAAA;AAAA;AAAA;AAAooC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAxpC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import App from './App';\n\n// 全局mixins，用于实现setData等功能，请勿删除！';\nimport zpMixins from '@/uni_modules/zp-mixins/index.js';\n\n\nimport Vue from 'vue';\n\nVue.use(zpMixins);\n\nVue.config.productionTip = false;\nApp.mpType = 'app';\nconst app = new Vue({\n    ...App\n});\napp.$mount();", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\n// app.js\nexport default {\n    data() {\n        return {};\n    },\n    onLaunch() {\n        // 展示本地存储能力\n        const logs = uni.getStorageSync('logs') || [];\n        logs.unshift(Date.now());\n        uni.setStorageSync('logs', logs);\n\n        // 登录\n        // wx.login({\n        //   success: res => {\n        //     // 发送 res.code 到后台换取 openId, sessionKey, unionId\n        //   }\n        // })\n\n        //版本自动更新\n        if (uni.canIUse('getUpdateManager')) {\n            const updateManager = uni.getUpdateManager();\n            updateManager.onCheckForUpdate(function (res) {\n                console.log('onCheckForUpdate====', res);\n                // 请求完新版本信息的回调\n                if (res.hasUpdate) {\n                    console.log('res.hasUpdate====');\n                    updateManager.onUpdateReady(function () {\n                        uni.showModal({\n                            title: '更新提示',\n                            content: '新版本已经准备好，是否重启应用？',\n                            success: function (res) {\n                                console.log('success====', res);\n                                // res: {errMsg: \"showModal: ok\", cancel: false, confirm: true}\n                                if (res.confirm) {\n                                    // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启\n                                    updateManager.applyUpdate();\n                                }\n                            }\n                        });\n                    });\n                    updateManager.onUpdateFailed(function () {\n                        // 新的版本下载失败\n                        uni.showModal({\n                            title: '已经有新版本了哟~',\n                            content: '新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~'\n                        });\n                    });\n                }\n            });\n        }\n    },\n    globalData: {\n        userInfo: null,\n        shareInfo: {\n            button: {\n                title: '嗨！来场知识比拼如何？',\n                imageUrl: 'https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250401/share_2.jpg?time=' + new Date().getTime()\n            },\n            menu: {\n                title: '这题好难，你能答对吗？',\n                imageUrl: 'https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250401/share_423.jpg?time=' + new Date().getTime()\n            }\n        }\n    }\n};\n</script>\n<style>\n@import './assets/css/animate.css'; /**app.wxss**/\n.container {\n    height: 100%;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: space-between;\n    padding: 200rpx 0;\n    box-sizing: border-box;\n}\n</style>\n", "import mod from \"-!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266620797\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}