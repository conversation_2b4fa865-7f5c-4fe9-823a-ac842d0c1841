{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/collect/collect.vue?2ad4", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/collect/collect.vue?97e9", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/collect/collect.vue?6a10", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/collect/collect.vue?c44d", "uni-app:///pages/collect/collect.vue", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/collect/collect.vue?dee0", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/collect/collect.vue?762d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "navigationBar", "data", "baseUrl", "region_area", "truename", "age", "phone", "area_name", "onetime", "onLoad", "onReady", "onShow", "onHide", "onUnload", "onPullDownRefresh", "onReachBottom", "onShareAppMessage", "methods", "get_truename", "that", "get_age", "get_phone", "change_address", "console", "sub", "uni", "title", "icon", "duration", "httpApi"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACuCz1B;AACA;AACA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;EACAC;IACA;IACAC;MACA;MACAC;QACAf;MACA;IACA;IAEA;IACAgB;MACA;MACAD;QACAd;MACA;IACA;IAEA;IACAgB;MACA;MACAF;QACAb;MACA;IACA;IAEA;IACAgB;MACA;MACA;MACAC;MACAJ;QACAhB;QACAI;MACA;IACA;IAEA;IACAiB;MACA;;MAEA;MACA;QACApB;QACAC;QACAC;QACAC;MACA;MACA;QACAY;UACAX;QACA;QACA;UACAiB;YACAC;YACAC;YACAC;UACA;UACAT;YACAX;UACA;QACA;UACAiB;YACAC;YACAC;YACAC;UACA;UACAT;YACAX;UACA;QACA;UACAiB;YACAC;YACAC;YACAC;UACA;UACAT;YACAX;UACA;QACA;UACAqB;YACAN;YACAE;cACAC;cACAC;cACAC;YACA;YACA;cACAT;gBACAX;cACA;YACA;UACA;QACA;MACA;QACAiB;UACAC;UACAC;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjMA;AAAA;AAAA;AAAA;AAAoiD,CAAgB,o6CAAG,EAAC,C;;;;;;;;;;;ACAxjD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/collect/collect.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/collect/collect.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./collect.vue?vue&type=template&id=6cfe335c&\"\nvar renderjs\nimport script from \"./collect.vue?vue&type=script&lang=js&\"\nexport * from \"./collect.vue?vue&type=script&lang=js&\"\nimport style0 from \"./collect.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/collect/collect.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./collect.vue?vue&type=template&id=6cfe335c&\"", "var components\ntry {\n  components = {\n    regionPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/region-picker/region-picker\" */ \"@/components/region-picker/region-picker.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./collect.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./collect.vue?vue&type=script&lang=js&\"", "<template>\n    <!-- pages/collect/collect.wxml -->\n    <view class=\"main-root-bg\">\n        <navigation-bar title=\"信息采集\" :back=\"false\" color=\"white\"></navigation-bar>\n        <view class=\"content_box\">\n            <view class=\"mes_box\">\n                <view class=\"mes\">\n                    <view class=\"mes_name\">姓名</view>\n                    <input type=\"text\" placeholder-class=\"pl\" :value=\"truename\" @input=\"get_truename\" class=\"zt_input\" placeholder=\"请输入真实姓名\" />\n                </view>\n                <view class=\"mes\">\n                    <view class=\"mes_name\">年龄</view>\n                    <input type=\"text\" placeholder-class=\"pl\" :value=\"age\" @input=\"get_age\" class=\"zt_input\" placeholder=\"请输入真实年龄\" />\n                </view>\n                <view class=\"mes\">\n                    <view class=\"mes_name\">手机号</view>\n                    <input type=\"text\" placeholder-class=\"pl\" :value=\"phone\" @input=\"get_phone\" class=\"zt_input\" placeholder=\"请输入真实手机号\" />\n                </view>\n                <view class=\"mes\">\n                    <view class=\"mes_name\">\n                        地区\n                        <text>(选择)</text>\n                    </view>\n                    <region-picker class=\"zt_input zt_input_1\" @change=\"change_address\" :value=\"region_area\">\n                        <image :src=\"baseUrl + '/style/images/collect_xl.png'\"></image>\n\n                        <view class=\"picker\">{{ region_area[0] }}</view>\n                    </region-picker>\n                </view>\n            </view>\n        </view>\n        <view class=\"btn_box\">\n            <view class=\"btn\" @tap=\"sub\">提 交</view>\n        </view>\n    </view>\n</template>\n\n<script>\nimport navigationBar from '@/components/navigation-bar/navigation-bar';\n// pages/collect/collect.js\nconst setting = require('../../http/env');\nconst httpApi = require('../../http/request'); //后续可以改为你自己的域名接口地址\n\nexport default {\n    components: {\n        navigationBar\n    },\n    data() {\n        return {\n            //默认域名\n            baseUrl: setting.baseUrl,\n            region_area: ['山东省'],\n            truename: '',\n            age: '',\n            phone: '',\n            area_name: '山东省',\n            onetime: true\n        };\n    },\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {},\n    /**\n     * 生命周期函数--监听页面初次渲染完成\n     */\n    onReady() {},\n    /**\n     * 生命周期函数--监听页面显示\n     */\n    onShow() {},\n    /**\n     * 生命周期函数--监听页面隐藏\n     */\n    onHide() {},\n    /**\n     * 生命周期函数--监听页面卸载\n     */\n    onUnload() {},\n    /**\n     * 页面相关事件处理函数--监听用户下拉动作\n     */\n    onPullDownRefresh() {},\n    /**\n     * 页面上拉触底事件的处理函数\n     */\n    onReachBottom() {},\n    /**\n     * 用户点击右上角分享\n     */\n    onShareAppMessage() {},\n    methods: {\n        // 姓名\n        get_truename: function (e) {\n            var that = this;\n            that.setData({\n                truename: e.detail.value\n            });\n        },\n\n        // 年龄\n        get_age: function (e) {\n            var that = this;\n            that.setData({\n                age: e.detail.value\n            });\n        },\n\n        // 手机号\n        get_phone: function (e) {\n            var that = this;\n            that.setData({\n                phone: e.detail.value\n            });\n        },\n\n        // 修改地区\n        change_address: function (e) {\n            var that = this;\n            var region_area = e.detail.value;\n            console.log('picker发送选择改变，携带值为', e.detail.value);\n            that.setData({\n                region_area: region_area,\n                area_name: region_area[0]\n            });\n        },\n\n        //确认发布\n        sub(e) {\n            var that = this;\n\n            //列表\n            var param = {\n                truename: that.truename,\n                age: that.age,\n                phone: that.phone,\n                area_name: that.area_name\n            };\n            if (that.onetime) {\n                that.setData({\n                    onetime: false\n                });\n                if (param.truename == '') {\n                    uni.showToast({\n                        title: '请输入姓名',\n                        icon: 'none',\n                        duration: 2000\n                    });\n                    that.setData({\n                        onetime: true\n                    });\n                } else if (param.age == '') {\n                    uni.showToast({\n                        title: '请输入年龄',\n                        icon: 'none',\n                        duration: 2000\n                    });\n                    that.setData({\n                        onetime: true\n                    });\n                } else if (param.phone == '') {\n                    uni.showToast({\n                        title: '请输入手机号',\n                        icon: 'none',\n                        duration: 2000\n                    });\n                    that.setData({\n                        onetime: true\n                    });\n                } else {\n                    httpApi.Post('/index/mesCollect', param).then((resInfo) => {\n                        console.log('resInfo', resInfo);\n                        uni.showToast({\n                            title: resInfo.msg,\n                            icon: 'none',\n                            duration: 2000\n                        });\n                        if (resInfo.status == -1) {\n                            that.setData({\n                                onetime: true\n                            });\n                        }\n                    });\n                }\n            } else {\n                uni.showToast({\n                    title: '请不要重复提交',\n                    icon: 'none',\n                    duration: 2000\n                });\n            }\n        }\n    }\n};\n</script>\n<style lang=\"scss\">\n@import '../../assets/css/theme.scss';\n@import '../../assets/css/common.scss'; /* pages/collect/collect.wxss */\n.main-root-bg {\n    background-image: url('https://game.shuguos.com/style/images/collect_bg.png');\n}\n.weui-navigation-bar__center text {\n    color: rgb(105, 71, 60);\n}\n\n.content_box {\n    height: 50%;\n    display: flex;\n    align-items: flex-end;\n    justify-content: center;\n}\n.mes_box {\n    width: 100%;\n    background: rgb(255, 205, 73);\n    padding: 60rpx 60rpx;\n}\n.mes {\n    width: 100%;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin: 20rpx 0;\n}\n.mes_name {\n    width: 180rpx;\n    height: 60rpx;\n    line-height: 60rpx;\n    color: rgb(105, 71, 60);\n    font-size: 28rpx;\n    font-weight: bold;\n    border-radius: 100rpx;\n    box-shadow: 0px 0px 10px 1px rgba(0, 0, 0, 0.05);\n    background: linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1));\n    text-align: center;\n}\n.mes_name text {\n    font-size: 22rpx;\n}\n.zt_input {\n    width: calc(100% - 210rpx);\n    height: 60rpx;\n    line-height: 60rpx;\n    color: rgb(105, 71, 60);\n    font-size: 28rpx;\n    font-weight: bold;\n    border-radius: 100rpx;\n    box-shadow: 0px 0px 10px 1px rgba(0, 0, 0, 0.05);\n    background: linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1));\n    text-align: center;\n}\n.pl {\n    font-weight: normal;\n    color: #999;\n    font-size: 26rpx;\n}\n.zt_input_1 {\n    position: relative;\n}\n.zt_input image {\n    width: 30rpx;\n    height: 30rpx;\n    position: absolute;\n    left: 20rpx;\n    top: 14rpx;\n}\n.btn_box {\n    width: 100%;\n    padding: 60rpx 60rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    box-sizing: border-box;\n}\n.btn {\n    width: 100%;\n    height: 80rpx;\n    line-height: 80rpx;\n    color: rgb(105, 71, 60);\n    font-size: 32rpx;\n    font-weight: bold;\n    border-radius: 100rpx;\n    box-shadow: 0px 0px 10px 1px rgba(0, 0, 0, 0.05);\n    background: linear-gradient(rgba(255, 205, 73, 1), rgb(194, 151, 33));\n    text-align: center;\n    margin-top: 20rpx;\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./collect.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./collect.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266622493\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}