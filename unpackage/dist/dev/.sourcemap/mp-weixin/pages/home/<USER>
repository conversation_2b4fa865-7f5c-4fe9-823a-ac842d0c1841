{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/home/<USER>", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/home/<USER>", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/home/<USER>", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/home/<USER>", "uni-app:///pages/home/<USER>", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/home/<USER>", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/home/<USER>"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "navigationBar", "scoreRecord", "coinRecord", "prize", "rankList", "msgContact", "userInfo", "gradeList", "nick_img", "remind", "blindBox", "data", "baseUrl", "wxCode", "nick<PERSON><PERSON>", "avatarUrl", "sex", "session_key", "openid", "sq_phone", "needAuth", "canIUseGetUserProfile", "wx_version", "grade_id", "phone", "unitid", "close_status", "close_tt", "unitid_copy", "index_title", "showRecord", "showCoinRecord", "showPrize", "showRank", "rankTab", "showNickImg", "showRemind", "showBlindBox", "quest_rule_id", "home_user_id", "musicVideo", "prizeList", "inviterId", "onLoad", "that", "uni", "console", "success", "onReady", "onShow", "onHide", "onUnload", "onPullDownRefresh", "onReachBottom", "onShareAppMessage", "title", "imageUrl", "path", "fail", "methods", "getUserProfile", "desc", "bindGetUserInfo", "wxGetInfo", "code", "httpApi", "getPhoneNumber", "encrypData", "ivData", "quxiao_phone", "jumpPk", "url", "shopClose", "toFirendVs", "getRobotCount", "Post", "user_id", "then", "closeClose", "openCoinRecord", "closeCoinRecord", "openPrize", "closePrize", "openRank", "closeRank", "closeGrade", "openRecord", "closeRecord", "jump_rule", "openNick", "<PERSON><PERSON><PERSON>", "close<PERSON><PERSON><PERSON>", "startGame", "toVsPage", "toPrizePage", "toKonwledgePage", "toBlindBox", "getUserInfoByApi", "getFirstOne", "getPrizeList", "getIndexTt", "getFlagexpired", "expired", "playVideo", "useWebAudioImplement", "stopVideo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,aAAa,sLAEN;AACP,KAAK;AACL;AACA,aAAa,gNAEN;AACP,KAAK;AACL;AACA,aAAa,0MAEN;AACP,KAAK;AACL;AACA,aAAa,sKAEN;AACP,KAAK;AACL;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,aAAa,oMAEN;AACP,KAAK;AACL;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAAk0B,CAAgB,kyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsIt1B;AACA;AACA;AACA;AAAA,eACA;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACA;MACAhB;QACAiB;QACAC;QACAC;MACA;MACAC;MACAC;MACA;MACAF;MACAG;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACA;;MAEAC;IACA;EACA;EACA;AACA;AACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACAlB;IACA;IACA;MACAG;IACA;;IAEA;IACA;IACA;MACA;MACA;QACAH;MACA;MACA;QACAG;MACA;MACAgB;IACA;MACA;MACAC;MACA;MACAD;IACA;MACA;IACA;IACAE;IACAF;MACAtC;MACAc;MACAmB;MACAD;MACAb;MACAG;MACAc;IACA;IACAG;MACAE;QACA;QACAzB;QACAwB;QACAF;UACAtB;QACA;MACA;IACA;;IAEA;IACA;MACA;QACAD;MACA;IACA;IACAwB;MACAE;QACAD;QACAF;UACA/B;QACA;MACA;IACA;;IAEA;IACA+B;EACA;EACA;AACA;AACA;EACAI;EACA;AACA;AACA;EACAC;IACA;IACA;IACA;MACA;MACA;IACA;EACA;EACA;AACA;AACA;EACAC;IACA;EAAA,CACA;EACA;AACA;AACA;EACAC;IACA;EAAA,CACA;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;IACA;IACA;IACA;MACAC;MACAC;MACAC;MACAV;QACA;QACAD;MACA;MACAY;QACA;MAAA;IAEA;EACA;EACAC;IACA;IACA;IACAC;MACA;MACAf;QACAgB;QACA;QACAd;UACAH;QACA;MACA;IACA;IAEA;IACAkB;MACA;IACA;IAEA;IACAC;MACA;MACAjB;MACA;QACA;UACAkB;UACAlD;UACAC;UACAC;UACAS;UACAG;UACAc;QACA;QACAuB;UACAnB;UACA;YACA;UACA;UACAF;YACA;YACA;YACA;YACA3B;YACAC;YACAC;UACA;QACA;MACA;QACAyB;UACAzB;QACA;MACA;IACA;IAEA;IACA+C;MACA;MACApB;MACA;QACAqB;QACAC;QACAJ;QACAlD;QACAC;QACAC;QACAC;QACAC;QACAO;QACAG;QACAc;MACA;MACAuB;QACA;QACApB;QACAD;UACAtC;UACAc;UACAD;QACA;QACA;UACA;UACA;UACA;YACAO;YACAC;UACA;UACA;YACAD;YACAC;UACA;UACAiB;YACAN;YACAC;YACAb;YACAC;YACAb;YACAC;UACA;QACA;;QAEA;QACA6B;QACA;QACA;UACAA;QACA;MACA;IACA;IAEA;IACAyB;MACA;MACAzB;QACAzB;MACA;IACA;IAEA;IACAmD;MACA;MACA;MACA;MACA;QACA1B;UACAN;UACAC;QACA;QACAM;UACA0B;QACA;MACA;IACA;IAEA;IACAC;MAAA;QAAA;QAAA;MACA;MACA;MACA;MACA;QACA/C;MACA;MACAwC;QACA;;QAEA;QACA;QACA;UACAvC;UACAC;QACA;QACA;UACAD;UACAC;QACA;QACAiB;UACAlB;UACAC;QACA;QACA;UACA;YACA;UACA;UACA;YACAkB;cACA0B;YACA;UACA;QACA;MACA;IACA;IAEAE;MACA5B;QACA0B;MACA;MACA;QACAnC;MACA;IACA;IAEA;IACAsC;MAAA;MACAT,QACAU;QACAC;MACA,GACAC;QACA;UACA;YACAzC;UACA;QACA;UACAS;YACA0B;UACA;QACA;MACA;IACA;IAEA;IACAO;MACA;MACAlC;QACAlB;MACA;IACA;IAEA;IACAqD;MACA;QACAhD;MACA;IACA;IAEAiD;MACA;QACAjD;MACA;IACA;IAEA;IACAkD;MACA;MACApC;QACA0B;MACA;IACA;IAEAW;MACA;QACAlD;MACA;IACA;IAEA;IACAmD;MACArC;MACA;QACAb;QACAC;MACA;IACA;IAEAkD;MACA;QACAnD;MACA;IACA;IAEAoD;MACA,+CACA,+BACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACAxD;MACA;IACA;IAEAyD;MACA;QACAzD;MACA;IACA;IAEA;IACA0D;MACA;MACA;MACA3C;QACA0B;MACA;IACA;IAEA;IACAkB;MACA;MACA3C;MACA;QACAX;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEAuD;MACA;QACAvD;MACA;MACA;IACA;IAEAwD;MACA;QACAvD;MACA;IACA;IAEA;IACAwD;MACA/C;QACA0B;MACA;IACA;IAEA;IACAsB;MACAhD;QACA0B;MACA;IACA;IAEA;IACAuB;MACAhD;MACAD;QACA0B;MACA;IACA;IAEA;IACAwB;MACAlD;QACA0B;MACA;IACA;IAEA;IACAyB;MACA;QACA3D;MACA;MACAQ;QACA0B;MACA;IACA;IAEA;IACA0B;MAAA;MACA;MACA;MACAhC,QACAU;QACAC;MACA,GACAC;QACA;UACAhC;UACA;UACAD;YACAtC;YACAc;UACA;QACA;UACA;UACAyB;UACAD;YACAtC;UACA;UACA;QACA;MACA;IACA;IAEA;IACA4F;MAAA;MACA;MACAjC,QACAU;QACAC;MACA,GACAC;QACA;UACA;QACA;QACA;UACA;YACAxC;UACA;QACA;MACA;IACA;IAEA;IACA8D;MAAA;MACAlC,QACAU;QACAC;MACA,GACAC;QACA/B;QACA;UACAL;UACAT;QACA;MACA;IACA;IAEA;IACAoE;MAAA;MACAnC;QACAnB;QACA;UACAjB;QACA;MACA;IACA;IAEA;IACAwE;MAAA;MACApC;QACA;QACApB;UACApB;UACAG;UACA0E;QACA;MACA;IACA;IAEA;IACAC;MACA;QACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7wBA;AAAA;AAAA;AAAA;AAAiiD,CAAgB,i6CAAG,EAAC,C;;;;;;;;;;;ACArjD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/home/<USER>", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/home/<USER>'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./home.vue?vue&type=template&id=92bb8f34&\"\nvar renderjs\nimport script from \"./home.vue?vue&type=script&lang=js&\"\nexport * from \"./home.vue?vue&type=script&lang=js&\"\nimport style0 from \"./home.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/home/<USER>\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=template&id=92bb8f34&\"", "var components\ntry {\n  components = {\n    userInfo: function () {\n      return import(\n        /* webpackChunkName: \"components/user-info/user-info\" */ \"@/components/user-info/user-info.vue\"\n      )\n    },\n    vanPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/./miniprogram_npm/@vant/weapp/popup/index\" */ \"./miniprogram_npm/@vant/weapp/popup/index\"\n      )\n    },\n    scoreRecord: function () {\n      return import(\n        /* webpackChunkName: \"components/score-record/score-record\" */ \"@/components/score-record/score-record.vue\"\n      )\n    },\n    coinRecord: function () {\n      return import(\n        /* webpackChunkName: \"components/coin-record/coin-record\" */ \"@/components/coin-record/coin-record.vue\"\n      )\n    },\n    prize: function () {\n      return import(\n        /* webpackChunkName: \"components/prize/prize\" */ \"@/components/prize/prize.vue\"\n      )\n    },\n    rankList: function () {\n      return import(\n        /* webpackChunkName: \"components/rank-list/rank-list\" */ \"@/components/rank-list/rank-list.vue\"\n      )\n    },\n    gradeList: function () {\n      return import(\n        /* webpackChunkName: \"components/grade-list/grade-list\" */ \"@/components/grade-list/grade-list.vue\"\n      )\n    },\n    nick_img: function () {\n      return import(\n        /* webpackChunkName: \"components/nick_img/nick_img\" */ \"@/components/nick_img/nick_img.vue\"\n      )\n    },\n    remind: function () {\n      return import(\n        /* webpackChunkName: \"components/remind/remind\" */ \"@/components/remind/remind.vue\"\n      )\n    },\n    blindBox: function () {\n      return import(\n        /* webpackChunkName: \"components/blind-box/blind-box\" */ \"@/components/blind-box/blind-box.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=script&lang=js&\"", "<template>\n    <view>\n        <!-- pages/home/<USER>\n        <view class=\"home-bg\">\n            <navigation-bar :title=\"index_title\" :back=\"false\" color=\"#189EF3\" />\n            <user-info :userInfo=\"userInfo\" @openRecord=\"openRecord\" @openNick=\"openNick\" />\n            <view class=\"home-content dis-flex\">\n                <view class=\"top-enter dis-flex\">\n                    <view class=\"btn-box\" hover-class=\"click_btn\" @tap=\"openRank\" data-tab=\"0\">\n                        <image src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/home_zongbang.png\" mode=\"\" class=\"enter-icon\" />\n                    </view>\n                    <view class=\"btn-box\" hover-class=\"click_btn\" @tap=\"openRank\" data-tab=\"1\">\n                        <image src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/home_ribang.png\" mode=\"\" class=\"enter-icon\" />\n                    </view>\n                </view>\n                <view class=\"bottom-center dis-flex\">\n                    <view hover-class=\"click_btn\" @tap=\"jump_rule\" data-type=\"rule\">\n                        <image src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/home_guize.png\" mode=\"\" class=\"icon-enter-rule\" />\n                    </view>\n                    <view hover-class=\"click_btn\" @tap=\"jump_rule\" data-type=\"reward\">\n                        <image src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/home_jiangpin.png\" mode=\"\" class=\"icon-enter-prize\" />\n                    </view>\n                </view>\n            </view>\n            <view class=\"home-footer common-image\">\n                <view class=\"dis-flex footer-enter\">\n                    <image\n                        class=\"btn-start\"\n                        src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/btn_center.png\"\n                        mode=\"\"\n                        hover-class=\"click_btn\"\n                        @tap=\"shopClose\"\n                        data-type=\"free\"\n                    />\n                    <view class=\"dis-flex btn-group\">\n                        <image class=\"btn-pk\" src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/btn_left.png\" mode=\"\" @tap=\"shopClose\" data-type=\"friend\" />\n                        <button class=\"auth-phone\" open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\" v-if=\"!userInfo.phone\">\n                            <image class=\"btn-prize\" src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/btn_right.png\" mode=\"\" />\n                        </button>\n                        <image v-else class=\"btn-prize\" src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/btn_right.png\" mode=\"\" @tap=\"toPrizePage\" />\n                    </view>\n                </view>\n                <!-- <msg-contact /> -->\n            </view>\n            <view class=\"fre_box\" v-if=\"close_status\">\n                <view class=\"fre_back\">\n                    <image src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/modal_bg_2.png\" mode=\"\" class=\"fre_img\" />\n                    <view class=\"fre_text fre_text_close\">{{ close_tt }}</view>\n                    <image :src=\"baseUrl + '/upload/img/icon-no.png'\" mode=\"\" class=\"fre_close_img\" @tap=\"closeClose\" />\n                </view>\n            </view>\n        </view>\n        <!-- 授权 -->\n        <view class=\"auth-view\" v-if=\"needAuth\">\n            <button v-if=\"canIUseGetUserProfile\" @tap=\"getUserProfile\">\n                <view class=\"fre_box\" v-if=\"quest_rule_id && home_user_id\">\n                    <view class=\"fre_back\">\n                        <image src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/modal_bg_2.png\" mode=\"\" class=\"fre_img\" />\n                        <view class=\"fre_text\">您的好友邀请您PK对战，快点击下方开始按钮吧</view>\n                        <view class=\"fre_view\">\n                            <view class=\"fre_btn common-cancel-btn\">进入比赛</view>\n                        </view>\n                    </view>\n                </view>\n            </button>\n            <button v-else open-type=\"getUserInfo\" @getuserinfo=\"bindGetUserInfo\">\n                <view class=\"fre_box\" v-if=\"quest_rule_id && home_user_id\">\n                    <view class=\"fre_back\">\n                        <image src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/modal_bg_2.png\" mode=\"\" class=\"fre_img\" />\n                        <view class=\"fre_text\">您的好友邀请您PK对战，快点击下方开始按钮吧</view>\n                        <view class=\"fre_view\">\n                            <view class=\"fre_btn common-cancel-btn\">进入比赛</view>\n                        </view>\n                    </view>\n                </view>\n            </button>\n        </view>\n        <!-- 手机号授权 -->\n        <view class=\"sq_phone_box_box\" v-if=\"sq_phone\">\n            <view class=\"sq_phone_box\">\n                <view class=\"sq_phone_name\">为保证您的游戏体验，需要获取您的头像手机号，使用小程序请点击确定哦。</view>\n                <view class=\"sq_phone_btn\">\n                    <view class=\"sq_phone_btn_1\" @tap=\"quxiao_phone\">取消</view>\n                    <button class=\"sq_phone_btn_1 sq_phone_btn_2\" open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\">确定</button>\n                </view>\n            </view>\n        </view>\n        <!-- 积分记录 -->\n        <van-popup :show=\"showRecord\">\n            <score-record v-if=\"showRecord\" @closeRecord=\"closeRecord\" />\n        </van-popup>\n        <!-- 书果币记录 -->\n        <van-popup :show=\"showCoinRecord\">\n            <coin-record v-if=\"showCoinRecord\" @closeCoinRecord=\"closeCoinRecord\" />\n        </van-popup>\n        <!-- 奖品弹窗 -->\n        <van-popup :show=\"showPrize\">\n            <prize @closePrize=\"closePrize\" :prizeList=\"prizeList\" />\n        </van-popup>\n        <!-- 排行榜 -->\n        <van-popup :show=\"showRank\">\n            <rank-list v-if=\"showRank\" @closeRank=\"closeRank\" :current-tab=\"rankTab\" :unitid=\"userInfo.unitid\" @openNick=\"openNick\" />\n        </van-popup>\n        <!-- 年级 -->\n        <van-popup :show=\"userInfo.grade_id == 0\">\n            <grade-list @closeGrade=\"closeGrade\" />\n        </van-popup>\n        <!-- 用户头像昵称 -->\n        <van-popup :show=\"showNickImg\">\n            <nick_img @closeNick=\"closeNick\" />\n        </van-popup>\n        <!-- 次数用尽提示 -->\n        <van-popup :show=\"showRemind\">\n            <remind @closeRemind=\"closeRemind\" @shopClose=\"toFirendVs\" />\n        </van-popup>\n        <!-- 盲盒 -->\n        <van-popup :show=\"showBlindBox\">\n            <blind-box @toBlindBox=\"toBlindBox\" />\n        </van-popup>\n    </view>\n</template>\n\n<script>\nimport navigationBar from '@/components/navigation-bar/navigation-bar';\nimport scoreRecord from '@/components/score-record/score-record';\nimport coinRecord from '@/components/coin-record/coin-record';\nimport prize from '@/components/prize/prize';\nimport rankList from '@/components/rank-list/rank-list';\nimport msgContact from '@/components/msg-contact/msg-contact';\nimport userInfo from '@/components/user-info/user-info';\nimport gradeList from '@/components/grade-list/grade-list';\nimport nick_img from '@/components/nick_img/nick_img';\nimport remind from '@/components/remind/remind';\nimport blindBox from '@/components/blind-box/blind-box';\n// pages/home/<USER>\nconst setting = require('../../http/env');\nconst httpApi = require('../../http/request'); //后续可以改为你自己的域名接口地址\nvar app = getApp();\nexport default {\n    components: {\n        navigationBar,\n        scoreRecord,\n        coinRecord,\n        prize,\n        rankList,\n        msgContact,\n        userInfo,\n        gradeList,\n        nick_img,\n        remind,\n        blindBox\n    },\n    data() {\n        return {\n            //默认域名\n            baseUrl: setting.baseUrl,\n            //授权\n            wxCode: '',\n            nickName: '',\n            avatarUrl: '',\n            sex: '',\n            session_key: '',\n            openid: '',\n            sq_phone: false,\n            needAuth: false,\n            canIUseGetUserProfile: false,\n            //版本号\n            wx_version: '',\n            //用户\n            userInfo: {\n                grade_id: '',\n                phone: '',\n                unitid: ''\n            },\n            close_status: false,\n            close_tt: '',\n            //唯一标识\n            unitid: '',\n            unitid_copy: '',\n            index_title: '',\n            //积分记录\n            showRecord: false,\n            //书果币记录\n            showCoinRecord: false,\n            //奖品弹窗\n            showPrize: false,\n            //排行榜\n            showRank: false,\n            rankTab: 0,\n            //头像昵称\n            showNickImg: false,\n            // 次数用尽提示\n            showRemind: false,\n            // 盲盒\n            showBlindBox: false,\n            //好友匹配的\n            quest_rule_id: 0,\n            home_user_id: 0,\n            musicVideo: null,\n            prizeList: [],\n            // 奖品列表\n\n            inviterId: null // 邀请人id\n        };\n    },\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n        var that = this;\n        //房主user_id\n        var home_user_id = options.home_user_id ? options.home_user_id : 0;\n        //答题匹配id\n        var quest_rule_id = options.quest_rule_id ? options.quest_rule_id : 0;\n        //唯一标识\n        var unitid = options.unitid ? options.unitid : '';\n        //唯一标识copy\n        var unitid_copy = options.unitid_copy ? options.unitid_copy : '';\n        if (unitid == 'undefined') {\n            unitid = '';\n        }\n        if (unitid_copy == 'undefined') {\n            unitid_copy = '';\n        }\n\n        // 如果存了用户信息，就不需要再次授权\n        var user = uni.getStorageSync('userInfo') || {};\n        if (!user || !user.id) {\n            var needAuth = true;\n            if (!unitid) {\n                unitid = user.unitid || '';\n            }\n            if (!unitid_copy) {\n                unitid_copy = user.unitid_copy || '';\n            }\n            that.getFlagexpired();\n        } else if (new Date().getTime() > new Date(user.expired).getTime()) {\n            // 如果过期了，重新授权\n            uni.setStorageSync('userInfo', '');\n            var needAuth = true;\n            that.getFlagexpired();\n        } else {\n            var needAuth = false;\n        }\n        console.log('携带参数unitid:', options.unitid, 'unitid_copy:', options.unitid_copy, '最终参数unitid:', unitid, 'unitid_copy:', unitid_copy);\n        that.setData({\n            userInfo: user,\n            needAuth: needAuth,\n            home_user_id: home_user_id,\n            quest_rule_id: quest_rule_id,\n            unitid: unitid,\n            unitid_copy: unitid_copy,\n            inviterId: options.inviterId || null\n        });\n        uni.getSystemInfo({\n            success: function (res) {\n                let wx_version = res.SDKVersion;\n                wx_version = wx_version.replace(/\\./g, '');\n                console.log('当前版本号: ' + wx_version);\n                that.setData({\n                    wx_version: wx_version\n                });\n            }\n        });\n\n        // 授权\n        if (uni.getUserProfile) {\n            this.setData({\n                canIUseGetUserProfile: true\n            });\n        }\n        uni.login({\n            success: function (resLogin) {\n                console.log('login', resLogin);\n                that.setData({\n                    wxCode: resLogin.code\n                });\n            }\n        });\n\n        //获取标题名称\n        that.getIndexTt();\n    },\n    /**\n     * 生命周期函数--监听页面初次渲染完成\n     */\n    onReady() {},\n    /**\n     * 生命周期函数--监听页面显示\n     */\n    onShow() {\n        // this.playVideo();\n        var user = uni.getStorageSync('userInfo');\n        if (user && user.id) {\n            this.getUserInfoByApi();\n            this.getPrizeList(); // 已经授权过，有用户信息，打开奖品弹窗\n        }\n    },\n    /**\n     * 生命周期函数--监听页面隐藏\n     */\n    onHide() {\n        // this.stopVideo();\n    },\n    /**\n     * 生命周期函数--监听页面卸载\n     */\n    onUnload() {\n        // this.stopVideo();\n    },\n    /**\n     * 页面相关事件处理函数--监听用户下拉动作\n     */\n    onPullDownRefresh() {},\n    /**\n     * 页面上拉触底事件的处理函数\n     */\n    onReachBottom() {},\n    /**\n     * 用户点击右上角分享\n     */\n    onShareAppMessage(e) {\n        var user = uni.getStorageSync('userInfo');\n        var path = '/pages/home/<USER>' + user.unitid + '&unitid_copy=' + user.unitid_copy + '&inviterId=' + uni.getStorageSync('userInfo').id;\n        return {\n            title: app.globalData.shareInfo[e.from].title,\n            imageUrl: app.globalData.shareInfo[e.from].imageUrl,\n            path: path,\n            success: function (res) {\n                // 转发成功\n                console.log(11212);\n            },\n            fail: function (res) {\n                // 转发失败\n            }\n        };\n    },\n    methods: {\n        // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认\n        // 开发者妥善保管用户快速填写的头像昵称，避免重复弹窗\n        getUserProfile() {\n            var that = this;\n            uni.getUserProfile({\n                desc: '用于完善会员资料',\n                // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写\n                success: (res) => {\n                    that.wxGetInfo(res.userInfo);\n                }\n            });\n        },\n\n        // open-type=\"getUserInfo\"\n        bindGetUserInfo(e) {\n            this.wxGetInfo(e.detail.userInfo);\n        },\n\n        // wx获取用户信息\n        wxGetInfo(wxUserInfo) {\n            var that = this;\n            console.log('wxUserInfo', wxUserInfo);\n            if (!that.openid) {\n                var params = {\n                    code: that.wxCode,\n                    nickName: '',\n                    avatarUrl: '',\n                    sex: '',\n                    unitid: that.unitid,\n                    unitid_copy: that.unitid_copy,\n                    inviterId: that.inviterId || undefined\n                };\n                httpApi.Post('/index/hasAccredit', params).then((resInfo) => {\n                    console.log('resInfo', resInfo);\n                    if (resInfo.status == -1) {\n                        return;\n                    }\n                    that.setData({\n                        // nickName: wxUserInfo.nickName,\n                        // avatarUrl: that.data.defaultAvatarUrl,\n                        // sex: wxUserInfo.gender,\n                        session_key: resInfo.data.session_key,\n                        openid: resInfo.data.openid,\n                        sq_phone: true\n                    });\n                });\n            } else {\n                that.setData({\n                    sq_phone: true\n                });\n            }\n        },\n\n        // 授权手机号\n        getPhoneNumber(e) {\n            var that = this;\n            console.log(e); // 动态令牌\n            const params = {\n                encrypData: e.detail.encryptedData,\n                ivData: e.detail.iv,\n                code: that.wxCode,\n                nickName: that.nickName,\n                avatarUrl: that.avatarUrl,\n                sex: that.gender,\n                session_key: that.session_key,\n                openid: that.openid,\n                unitid: that.unitid,\n                unitid_copy: that.unitid_copy,\n                inviterId: that.inviterId || undefined\n            };\n            httpApi.Post('/index/accreditNickPhone', params).then((res) => {\n                var user = uni.getStorageSync('userInfo') || {};\n                uni.setStorageSync('userInfo', Object.assign({}, user, res.data));\n                that.setData({\n                    userInfo: res.data,\n                    needAuth: false,\n                    sq_phone: false\n                });\n                if (res.data.is_close == 1 || res.data.is_end == 1) {\n                    var close_status = false;\n                    var close_tt = '';\n                    if (res.data.is_close == 1) {\n                        close_status = true;\n                        close_tt = '该门店已关闭，暂时不能游戏！';\n                    }\n                    if (res.data.is_end == 1) {\n                        close_status = true;\n                        close_tt = '活动已结束，暂时不能游戏！';\n                    }\n                    that.setData({\n                        quest_rule_id: 0,\n                        home_user_id: 0,\n                        close_status: close_status,\n                        close_tt: close_tt,\n                        nickName: res.data.nickname,\n                        avatarUrl: res.data.headimg\n                    });\n                }\n\n                // 已经授权过，有用户信息，打开奖品弹窗\n                that.getPrizeList();\n                //跳转好友对战\n                if (res.data.grade_id != 0) {\n                    that.jumpPk();\n                }\n            });\n        },\n\n        //取消手机号\n        quxiao_phone(e) {\n            var that = this;\n            that.setData({\n                sq_phone: false\n            });\n        },\n\n        //跳转好友对战\n        jumpPk(e) {\n            var that = this;\n            var quest_rule_id = that.quest_rule_id;\n            var home_user_id = that.home_user_id;\n            if (quest_rule_id && home_user_id) {\n                that.setData({\n                    quest_rule_id: 0,\n                    home_user_id: 0\n                });\n                uni.navigateTo({\n                    url: '../friendVs/friendVs?quest_rule_id=' + quest_rule_id + '&home_user_id=' + home_user_id\n                });\n            }\n        },\n\n        //是否关闭了门店\n        shopClose(e) {\n            var that = this;\n            var type = e.currentTarget?.dataset?.type;\n            var user = that.userInfo;\n            var params = {\n                unitid: user.unitid\n            };\n            httpApi.Post('/index/shopClose', params).then((res) => {\n                // console.log(\"data\", res);\n\n                var close_status = false;\n                var close_tt = '';\n                if (res.is_close == 1) {\n                    close_status = true;\n                    close_tt = '该门店已关闭，暂时不能游戏！';\n                }\n                if (res.is_end == 1) {\n                    close_status = true;\n                    close_tt = '活动已结束，暂时不能游戏！';\n                }\n                that.setData({\n                    close_status: close_status,\n                    close_tt: close_tt\n                });\n                if (res.is_close == 0 && res.is_end == 0) {\n                    if (type == 'free') {\n                        this.getRobotCount();\n                    }\n                    if (type == 'friend') {\n                        uni.navigateTo({\n                            url: '/pages/friendVs/friendVs'\n                        });\n                    }\n                }\n            });\n        },\n\n        toFirendVs() {\n            uni.navigateTo({\n                url: '/pages/friendVs/friendVs'\n            });\n            this.setData({\n                showRemind: false\n            });\n        },\n\n        // 查询今天能和机器人对战几次\n        getRobotCount() {\n            httpApi\n                .Post('/index/lastRobotCount', {\n                    user_id: uni.getStorageSync('userInfo').id\n                })\n                .then((res) => {\n                    if (res.limit <= res.count) {\n                        this.setData({\n                            showRemind: true\n                        });\n                    } else {\n                        uni.navigateTo({\n                            url: '/pages/pk-loading/pk-loading'\n                        });\n                    }\n                });\n        },\n\n        //关闭弹窗\n        closeClose(e) {\n            var that = this;\n            that.setData({\n                close_status: false\n            });\n        },\n\n        // 打开书果币记录\n        openCoinRecord() {\n            this.setData({\n                showCoinRecord: true\n            });\n        },\n\n        closeCoinRecord() {\n            this.setData({\n                showCoinRecord: false\n            });\n        },\n\n        // 打开奖品\n        openPrize() {\n            // 跳转页面，后台配置\n            uni.navigateTo({\n                url: '/pages/prize-list/prize-list'\n            });\n        },\n\n        closePrize() {\n            this.setData({\n                showPrize: false\n            });\n        },\n\n        // 打开排行榜\n        openRank(e) {\n            console.log('openRank', e.currentTarget.dataset.tab);\n            this.setData({\n                showRank: true,\n                rankTab: e.currentTarget.dataset.tab\n            });\n        },\n\n        closeRank() {\n            this.setData({\n                showRank: false\n            });\n        },\n\n        closeGrade(e) {\n            this.setData({\n                ['userInfo.grade_id']: e.detail\n            });\n            //跳转好友对战\n            this.jumpPk();\n        },\n\n        //积分记录\n        openRecord() {\n            this.setData({\n                showRecord: true\n            });\n        },\n\n        closeRecord() {\n            this.setData({\n                showRecord: false\n            });\n        },\n\n        //跳转规则\n        jump_rule(e) {\n            var that = this;\n            var type = e.currentTarget.dataset.type;\n            uni.navigateTo({\n                url: '../rule/rule?type=' + type\n            });\n        },\n\n        //头像昵称\n        openNick(e) {\n            var that = this;\n            console.log('that.data.userInfo.nickname', e);\n            this.setData({\n                showNickImg: true\n            });\n            // if (that.data.userInfo.nickname == \"微信用户\" || e.detail?.jumpIf) {\n            //   this.setData({\n            //     showNickImg: true,\n            //   });\n            // }\n        },\n\n        closeNick(e) {\n            this.setData({\n                showNickImg: false\n            });\n            this.getUserInfoByApi();\n        },\n\n        closeRemind() {\n            this.setData({\n                showRemind: false\n            });\n        },\n\n        // 开始比赛\n        startGame() {\n            uni.navigateTo({\n                url: '/pages/pk-loading/pk-loading'\n            });\n        },\n\n        // 跳转到好友对战\n        toVsPage() {\n            uni.navigateTo({\n                url: '/pages/friendVs/friendVs'\n            });\n        },\n\n        // 跳转获奖中心\n        toPrizePage() {\n            console.log('iris');\n            uni.navigateTo({\n                url: '/pages/prize-list/prize-list'\n            });\n        },\n\n        // 跳转到知识图谱\n        toKonwledgePage() {\n            uni.navigateTo({\n                url: '/pages/knowledge-new/knowledge-new'\n            });\n        },\n\n        // 跳转盲盒详情\n        toBlindBox() {\n            this.setData({\n                showBlindBox: false\n            });\n            uni.navigateTo({\n                url: '/pages/blind-box-detail/blind-box-detail'\n            });\n        },\n\n        // 获取用户信息\n        getUserInfoByApi() {\n            var that = this;\n            var user = that.userInfo;\n            httpApi\n                .Post('/index/getUser', {\n                    user_id: user.id\n                })\n                .then((res) => {\n                    if (res.status == -1) {\n                        uni.setStorageSync('userInfo', '');\n                        var needAuth = true;\n                        that.setData({\n                            userInfo: {},\n                            needAuth: needAuth\n                        });\n                    } else {\n                        var user = uni.getStorageSync('userInfo') || {};\n                        uni.setStorageSync('userInfo', Object.assign({}, user, res.data));\n                        that.setData({\n                            userInfo: res.data\n                        });\n                        this.getFirstOne();\n                    }\n                });\n        },\n\n        // 是否第一次对决\n        getFirstOne() {\n            // 不存在邀请且第一次打开得到盲盒\n            httpApi\n                .Post('/index/firstOne', {\n                    user_id: this.userInfo.id\n                })\n                .then((res) => {\n                    if (res.status) {\n                        return;\n                    }\n                    if (res.data.flag == 0) {\n                        this.setData({\n                            showBlindBox: true\n                        });\n                    }\n                });\n        },\n\n        // 获取奖品列表\n        getPrizeList() {\n            httpApi\n                .Post('/index/getUserWin', {\n                    user_id: uni.getStorageSync('userInfo').id\n                })\n                .then((res) => {\n                    console.log(res);\n                    this.setData({\n                        prizeList: res.data,\n                        showPrize: res.data.length ? true : false\n                    });\n                });\n        },\n\n        // 获取标题名称\n        getIndexTt() {\n            httpApi.Post('/index/getIndexTt', {}, '').then((res) => {\n                console.log(res);\n                this.setData({\n                    index_title: res.data.index_title\n                });\n            });\n        },\n\n        // 获取用户过期时间\n        getFlagexpired() {\n            httpApi.Get('/index/flagexpired', {}, '').then((res) => {\n                // 存入门店标识\n                uni.setStorageSync('userInfo', {\n                    unitid: this.unitid,\n                    unitid_copy: this.unitid_copy,\n                    expired: res.data.expired\n                });\n            });\n        },\n\n        // 播放音频\n        playVideo() {\n            this.musicVideo = uni.createInnerAudioContext({\n                useWebAudioImplement: true\n            });\n            this.musicVideo.loop = true; // 循环\n            this.musicVideo.src = 'https://game.shuguos.com/upload/video/video-home.mp3';\n            this.musicVideo.play(); // 播放\n        },\n\n        // 停止播放音频并释放\n        stopVideo() {\n            this.musicVideo.stop(); // 停止音频资源\n            this.musicVideo.destroy(); // 释放音频资源\n        }\n    }\n};\n</script>\n<style lang=\"scss\">\n@import '../../assets/css/theme.scss';\n@import '../../assets/css/common.scss';\n@import '../../assets/css/self-animate.scss'; /* pages/home/<USER>/\n\n.home-bg {\n    min-height: 1624rpx;\n    background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/home_bg.png');\n    background-size: 100% 100%;\n    @extend .main-root-bg;\n}\n\n/* 中间部分*/\n.home-content {\n    /* height: 100%;*/\n    flex-direction: column;\n\n    .icon-enter-rule,\n    .icon-enter-prize {\n        width: 81rpx;\n        height: 179rpx;\n    }\n    .top-enter {\n        margin-top: 170rpx;\n        flex-direction: column;\n\n        .btn-box {\n            width: fit-content;\n        }\n\n        .enter-icon {\n            margin-bottom: 21rpx;\n            margin-left: 8rpx;\n            width: 95rpx;\n            height: 106rpx;\n        }\n    }\n\n    .bottom-center {\n        margin-top: 160rpx;\n        justify-content: space-between;\n    }\n    .right-enter {\n        margin-top: 570rpx;\n    }\n}\n\n/* footer*/\n.home-footer {\n    padding: 78rpx 8rpx;\n\n    .footer-enter {\n        flex-direction: column;\n        align-items: center;\n\n        .btn-start {\n            width: 394rpx;\n            height: 214rpx;\n            margin-bottom: 23rpx;\n        }\n\n        .btn-group {\n            .btn-pk {\n                width: 325rpx;\n                height: 171rpx;\n            }\n\n            .btn-prize {\n                @extend .btn-pk;\n                margin-left: 30rpx;\n            }\n        }\n    }\n    .icon-double {\n        position: absolute;\n        top: -60rpx;\n        left: 30rpx;\n        width: 200rpx;\n        height: 80rpx;\n    }\n}\n\n/* auth-view*/\n.auth-view {\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    top: 0;\n    left: 0;\n    z-index: 99;\n    button {\n        width: 100%;\n        height: 100%;\n        background-color: transparent;\n    }\n}\n.auth-phone {\n    background: transparent;\n    padding: 0;\n}\n\n/* 邀请对战弹窗*/\n.fre_box {\n    background-color: var(--overlay-background-color, rgba(0, 0, 0, 0.7));\n    height: 100%;\n    left: 0;\n    position: fixed;\n    top: 0;\n    width: 100%;\n}\n.fre_back {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    height: 100%;\n    flex-direction: column;\n}\n.fre_img {\n    width: 501rpx;\n    height: 394rpx;\n}\n.fre_close_img {\n    width: 38rpx;\n    height: 38rpx;\n    margin-top: 50rpx;\n}\n.fre_text {\n    position: absolute;\n    font-size: 28rpx;\n    color: white;\n    line-height: 1.8;\n    text-align: center;\n    width: 350rpx;\n}\n.fre_text_close {\n    margin-top: 62rpx;\n}\n.fre_view {\n    position: relative;\n    margin-top: 50rpx;\n}\n\n/* 手机号授权*/\n.sq_phone_box_box {\n    background-color: var(--overlay-background-color, rgba(0, 0, 0, 0.3));\n    height: 100%;\n    left: 0;\n    position: fixed;\n    top: 0;\n    width: 100%;\n    z-index: 100;\n    display: flex;\n    justify-content: center;\n}\n.sq_phone_box {\n    display: flex;\n    flex-direction: column;\n    background: #fff;\n    position: absolute;\n    bottom: 50rpx;\n    border-radius: 16rpx;\n    padding: 40rpx;\n    width: 590rpx;\n}\n.sq_phone_btn {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 60rpx 60rpx 0 60rpx;\n}\n.sq_phone_btn_1 {\n    width: 176rpx !important;\n    height: 80rpx !important;\n    text-align: center;\n    color: #333;\n    border-radius: 8rpx;\n    border: 2rpx solid #666;\n    line-height: 76rpx;\n    font-size: 32rpx;\n    margin: 0 auto;\n    padding: 0;\n    box-sizing: border-box;\n}\n.sq_phone_btn_2 {\n    color: #fff;\n    background: #4aaaf8 !important;\n    border: 2rpx solid #4aaaf8 !important;\n}\n.sq_phone_name {\n    font-size: 32rpx;\n    color: #333333;\n    line-height: 46rpx;\n}\n.mend_box_box {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n.mend_box {\n    height: 200rpx;\n    background: #fff;\n    width: 80%;\n    border-radius: 20rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n.mend_text {\n    color: red;\n    text-align: center;\n    font-weight: bold;\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266622299\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}