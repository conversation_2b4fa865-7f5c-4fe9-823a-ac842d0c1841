{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/result/result.vue?767d", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/result/result.vue?c3ae", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/result/result.vue?179b", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/result/result.vue?b084", "uni-app:///pages/result/result.vue", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/result/result.vue?fe41", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/result/result.vue?cabf"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "navigationBar", "userInfo", "msgContact", "cheer", "double", "remind", "data", "baseUrl", "quest_game_id", "list", "is_success", "initiate_user", "headimg", "nickname", "sorce", "match_user", "type", "tis", "score_double", "showCheer", "showDouble", "count", "showBaseUrl", "showRemind", "continue_flag", "user", "onLoad", "uni", "url", "that", "onReady", "onShow", "onHide", "onUnload", "onPullDownRefresh", "onReachBottom", "onShareAppMessage", "title", "imageUrl", "path", "success", "console", "fail", "methods", "getResult", "user_id", "httpApi", "res", "setTimeout", "toKonwledgePage", "startGame", "frendGame", "frendGameOne", "back", "closeDb", "closeChe", "getUserInfoByApi", "Post", "then"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,aAAa,sLAEN;AACP,KAAK;AACL;AACA,aAAa,sKAEN;AACP,KAAK;AACL;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgFx1B;AACA;AACA;AACA;AAAA,eAEA;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;MAEA;MACAC;MAEA;MACAC;QACAC;QAEAC;UACAD;UACAE;UACAC;UACAC;QACA;QAEAC;UACAL;UACAE;UACAC;UACAC;QACA;QAEAE;QACAC;QACAC;MACA;MAEA;MACAC;MAEA;MACAC;MAEAnB;MAEA;MACAoB;MAEAC;MAEA;MACAC;MAEA;MACAC;MAEAC;IACA;EACA;EACA;AACA;AACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACAC;QACAC;MACA;IACA;;IAEA;IACAC;MACAJ;MACAjB;MACAa;MACAG;IACA;;IAEA;IACAK;IACAA;EACA;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;IACA;IACA;IACA;MACAC;MACAC;MACAC;MACAC;QACA;QACAC;MACA;MACAC;QACA;MAAA;IAEA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACA;QACAC;QACArC;MACA;MACA;MACAsC;QAAA;QACA;QACA;UACA1B;QACA;QACA;UACA;UACA2B;QACA;UACA;UACAA;QACA;QACAlB;UACApB;UACAW;UACAE;QACA;QACA0B;UACA;YACA5B;UACA;QACA;MACA;IACA;IAEA;IACA6B;MACAtB;QACAC;MACA;IACA;IAEA;IACAsB;MACAvB;QACAC;MACA;IACA;IAEA;IACAuB;MACA;QACA;UACA5B;QACA;QACA;MACA;MACA;MACAI;QACAC;MACA;IACA;IAEAwB;MACAzB;QACAC;MACA;IACA;IAEA;IACAyB;MACA;MACAZ;MACA;QACAZ;UACAV;QACA;MACA;QACAQ;UACAC;QACA;MACA;IACA;IAEA;IACA0B;MACA;MACAzB;QACAT;MACA;IACA;IAEA;IACAmC;MACA;MACA1B;QACAV;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEAqC;MACA;MACA;MACAV,QACAW;QACAZ;MACA,GACAa;QACA;QACA/B;QACAE;UACA5B;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClVA;AAAA;AAAA;AAAA;AAAmiD,CAAgB,m6CAAG,EAAC,C;;;;;;;;;;;ACAvjD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/result/result.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/result/result.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./result.vue?vue&type=template&id=8b0659b4&\"\nvar renderjs\nimport script from \"./result.vue?vue&type=script&lang=js&\"\nexport * from \"./result.vue?vue&type=script&lang=js&\"\nimport style0 from \"./result.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/result/result.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./result.vue?vue&type=template&id=8b0659b4&\"", "var components\ntry {\n  components = {\n    userInfo: function () {\n      return import(\n        /* webpackChunkName: \"components/user-info/user-info\" */ \"@/components/user-info/user-info.vue\"\n      )\n    },\n    vanPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/./miniprogram_npm/@vant/weapp/popup/index\" */ \"./miniprogram_npm/@vant/weapp/popup/index\"\n      )\n    },\n    cheer: function () {\n      return import(\n        /* webpackChunkName: \"components/cheer/cheer\" */ \"@/components/cheer/cheer.vue\"\n      )\n    },\n    double: function () {\n      return import(\n        /* webpackChunkName: \"components/double/double\" */ \"@/components/double/double.vue\"\n      )\n    },\n    remind: function () {\n      return import(\n        /* webpackChunkName: \"components/remind/remind\" */ \"@/components/remind/remind.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./result.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./result.vue?vue&type=script&lang=js&\"", "<template>\n    <view>\n        <!-- pages/result/result.wxml -->\n        <view class=\"main-root-bg\">\n            <navigation-bar title=\"游戏结果\" color=\"#189EF3\" @back=\"back\" :delta=\"0\" />\n            <view class=\"result-user-info\">\n                <user-info :userInfo=\"userInfo\" />\n            </view>\n            <view :class=\"'result-bg common-image ' + (list.is_success == 0 ? 'result_error' : '') + ' ' + (list.is_success == 2 ? 'result_ping' : '')\">\n                <view class=\"result-text\" v-if=\"list.is_success == 1\">隐藏的高手是你啦，答题继续， 快乐不停！</view>\n                <view class=\"result-text\" v-if=\"list.is_success == 0\">失败，很遗憾，就差一点点， 继续努力！</view>\n                <view class=\"dis-flex-center\">\n                    <view class=\"flex-full result-pk\">\n                        <image :src=\"baseUrl + '/upload/img/icon-success.png'\" mode=\"\" class=\"icon-success\" v-if=\"list.is_success != 2 && list.initiate_user.is_success == 1\" />\n                        <view class=\"result-photo\">\n                            <image\n                                v-if=\"!list.initiate_user.headimg\"\n                                src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250327/headimg.png\"\n                                mode=\"aspectFill\"\n                                class=\"vs-me-image-img\"\n                            />\n                            <image v-else-if=\"!showBaseUrl\" :src=\"list.initiate_user.headimg\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                            <image v-else :src=\"baseUrl + list.initiate_user.headimg\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                        </view>\n                        <view class=\"result-name overFlow\">{{ list.initiate_user.nickname }}</view>\n                        <view :class=\"'result-score ' + (list.initiate_user.is_success == 1 ? 'result-success-score' : '')\">\n                            {{ list.initiate_user.sorce }}\n                        </view>\n                    </view>\n                    <view class=\"flex-full result-pk\">\n                        <image :src=\"baseUrl + '/upload/img/icon-success.png'\" mode=\"\" class=\"icon-success\" v-if=\"list.is_success != 2 && list.match_user.is_success == 1\" />\n                        <view class=\"result-photo\">\n                            <image\n                                v-if=\"!list.match_user.headimg\"\n                                src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250327/headimg.png\"\n                                mode=\"aspectFill\"\n                                class=\"vs-me-image-img\"\n                            />\n                            <image v-else :src=\"baseUrl + list.match_user.headimg\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                        </view>\n                        <view class=\"result-name overFlow\">{{ list.match_user.nickname }}</view>\n                        <view :class=\"'result-score ' + (list.match_user.is_success == 1 ? 'result-success-score' : '')\">\n                            {{ list.match_user.sorce }}\n                        </view>\n                    </view>\n                </view>\n            </view>\n            <!-- 按钮 -->\n            <view class=\"dis-flex-center btn-view\">\n                <view class=\"btn-list common-image flex-full dis-flex again-no-btn\" v-if=\"count == 0 && list.type == 1\">再来一局（剩余：{{ count }}次）</view>\n                <view class=\"btn-list common-image flex-full dis-flex again-btn\" v-else-if=\"count > 0 && list.type == 1\" @tap=\"startGame\">再来一局（剩余：{{ count }}次）</view>\n                <view class=\"btn-list common-image flex-full dis-flex again-btn\" v-else-if=\"list.type != 1\" @tap=\"frendGame\">跟TA再来一局</view>\n            </view>\n            <view open-type=\"share\" class=\"btn-list common-image flex-full dis-flex share-btn-red\" @tap=\"frendGameOne\">邀请新的好友对战翻倍更高哦</view>\n            <!-- <view class=\"msg-view\">\n    <msg-contact />\n  </view> -->\n        </view>\n        <!-- 自由、提示 -->\n        <van-popup :show=\"showCheer\">\n            <cheer v-if=\"showCheer\" :tis=\"list.tis\" :showContinue=\"!(count == 0 && list.type == 1)\" @closeChe=\"closeChe\" />\n        </van-popup>\n        <!-- 翻倍提示 -->\n        <van-popup :show=\"showDouble\">\n            <double @closeDb=\"closeDb\" :num=\"list.score_double\" />\n        </van-popup>\n        <!-- 次数用尽提示 -->\n        <van-popup :show=\"showRemind\">\n            <remind @inviteNew=\"frendGameOne\" :isFirend=\"true\" :noShare=\"true\" />\n        </van-popup>\n    </view>\n</template>\n\n<script>\nimport navigationBar from '@/components/navigation-bar/navigation-bar';\nimport userInfo from '@/components/user-info/user-info';\nimport msgContact from '@/components/msg-contact/msg-contact';\nimport cheer from '@/components/cheer/cheer';\nimport double from '@/components/double/double';\nimport remind from '@/components/remind/remind';\n// pages/result/result.js\nvar app = getApp();\nconst setting = require('../../http/env');\nconst httpApi = require('../../http/request'); //后续可以改为你自己的域名接口地址\n\nexport default {\n    components: {\n        navigationBar,\n        userInfo,\n        msgContact,\n        cheer,\n        double,\n        remind\n    },\n    data() {\n        return {\n            //默认域名\n            baseUrl: setting.baseUrl,\n\n            //答题比赛id\n            quest_game_id: 0,\n\n            //数据\n            list: {\n                is_success: 0,\n\n                initiate_user: {\n                    is_success: 0,\n                    headimg: '',\n                    nickname: '',\n                    sorce: ''\n                },\n\n                match_user: {\n                    is_success: 0,\n                    headimg: '',\n                    nickname: '',\n                    sorce: ''\n                },\n\n                type: 0,\n                tis: '',\n                score_double: ''\n            },\n\n            //显示自由提示\n            showCheer: false,\n\n            //显示翻倍提示\n            showDouble: false,\n\n            userInfo: {},\n\n            // 剩余比赛次数\n            count: null,\n\n            showBaseUrl: false,\n\n            // 次数用尽提示\n            showRemind: false,\n\n            // 是否能继续跟TA对战 0-是 1-否\n            continue_flag: 0,\n\n            user: ''\n        };\n    },\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n        var that = this;\n        //答题比赛id\n        var quest_game_id = options.quest_game_id ? options.quest_game_id : 0;\n        //用户数据\n        var user = uni.getStorageSync('userInfo');\n        //参数判断\n        if (!user || !quest_game_id) {\n            uni.redirectTo({\n                url: '../home/<USER>'\n            });\n        }\n\n        //保存数据\n        that.setData({\n            user: user,\n            quest_game_id: quest_game_id,\n            count: Number(options.robot_flag) ? options.count : null,\n            continue_flag: options.continue_flag\n        });\n\n        //获取结果\n        that.getResult();\n        that.getUserInfoByApi();\n    },\n    /**\n     * 生命周期函数--监听页面初次渲染完成\n     */\n    onReady() {},\n    /**\n     * 生命周期函数--监听页面显示\n     */\n    onShow() {},\n    /**\n     * 生命周期函数--监听页面隐藏\n     */\n    onHide() {},\n    /**\n     * 生命周期函数--监听页面卸载\n     */\n    onUnload() {},\n    /**\n     * 页面相关事件处理函数--监听用户下拉动作\n     */\n    onPullDownRefresh() {},\n    /**\n     * 页面上拉触底事件的处理函数\n     */\n    onReachBottom() {},\n    /**\n     * 用户点击右上角分享\n     */\n    onShareAppMessage(e) {\n        var user = uni.getStorageSync('userInfo');\n        var path = '/pages/home/<USER>' + user.unitid + '&unitid_copy=' + user.unitid_copy + '&inviterId=' + uni.getStorageSync('userInfo').id;\n        return {\n            title: app.globalData.shareInfo[e.from].title,\n            imageUrl: app.globalData.shareInfo[e.from].imageUrl,\n            path: path,\n            success: function (res) {\n                // 转发成功\n                console.log(11212);\n            },\n            fail: function (res) {\n                // 转发失败\n            }\n        };\n    },\n    methods: {\n        //获取结果数据\n        getResult(e) {\n            var that = this;\n            var param = {\n                user_id: that.user.id,\n                quest_game_id: that.quest_game_id\n            };\n            var user = uni.getStorageSync('userInfo');\n            httpApi.Post('/index/comGame', param).then((res) => {\n                var showDouble = false;\n                if (res.data.score_double > 1) {\n                    showDouble = true;\n                }\n                if (res.data.initiate_user_id == user.id) {\n                    // 自己是邀请人\n                    res.data.is_success = res.data.initiate_user.sorce > res.data.match_user.sorce ? 1 : res.data.initiate_user.sorce < res.data.match_user.sorce ? 0 : 2;\n                } else {\n                    // 自己是被邀请人\n                    res.data.is_success = res.data.initiate_user.sorce > res.data.match_user.sorce ? 0 : res.data.initiate_user.sorce < res.data.match_user.sorce ? 1 : 2;\n                }\n                that.setData({\n                    list: res.data,\n                    showDouble: showDouble,\n                    showBaseUrl: !res.data?.initiate_user?.headimg?.includes('http')\n                });\n                setTimeout(() => {\n                    this.setData({\n                        showDouble: false\n                    });\n                }, 4000);\n            });\n        },\n\n        // 跳转到知识图谱\n        toKonwledgePage() {\n            uni.navigateTo({\n                url: '/pages/knowledge-new/knowledge-new'\n            });\n        },\n\n        //自由匹配再来一局\n        startGame(e) {\n            uni.redirectTo({\n                url: '/pages/pk-loading/pk-loading'\n            });\n        },\n\n        //好友匹配再来一局\n        frendGame(e) {\n            if (this.continue_flag == 1) {\n                this.setData({\n                    showRemind: true\n                });\n                return;\n            }\n            var that = this;\n            uni.redirectTo({\n                url: '/pages/friendVs/friendVs?quest_game_id=' + that.list.id + '&nedd_back=1'\n            });\n        },\n\n        frendGameOne(e) {\n            uni.redirectTo({\n                url: '/pages/friendVs/friendVs?nedd_back=1'\n            });\n        },\n\n        //退出提示\n        back() {\n            var that = this;\n            console.log('exit');\n            if (that.list.tis) {\n                that.setData({\n                    showCheer: true\n                });\n            } else {\n                uni.redirectTo({\n                    url: '../home/<USER>'\n                });\n            }\n        },\n\n        //关闭翻倍\n        closeDb(e) {\n            var that = this;\n            that.setData({\n                showDouble: false\n            });\n        },\n\n        //继续答题\n        closeChe(e) {\n            var that = this;\n            that.setData({\n                showCheer: false\n            });\n            if (that.list.type == 1) {\n                this.startGame();\n            } else {\n                this.frendGame();\n            }\n        },\n\n        getUserInfoByApi() {\n            var that = this;\n            var user = uni.getStorageSync('userInfo');\n            httpApi\n                .Post('/index/getUser', {\n                    user_id: user.id\n                })\n                .then((res) => {\n                    var user = uni.getStorageSync('userInfo') || {};\n                    uni.setStorageSync('userInfo', Object.assign({}, user, res.data));\n                    that.setData({\n                        userInfo: res.data\n                    });\n                });\n        }\n    }\n};\n</script>\n<style lang=\"scss\">\n@import '../../assets/css/theme.scss';\n@import '../../assets/css/common.scss'; /* pages/result/result.wxss */\n.result-user-info {\n    padding: 48rpx 35rpx;\n    padding-bottom: 0;\n}\n.result-bg {\n    background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/end_bg_2.png');\n    padding-top: 325rpx;\n    box-sizing: border-box;\n    width: 686rpx;\n    height: 689rpx;\n    margin: 42rpx auto 0 auto;\n\n    .result-text {\n        font-size: 28rpx;\n        color: white;\n        text-align: center;\n        margin: 0 190rpx;\n    }\n    .result-pk {\n        position: relative;\n        margin-top: 26rpx;\n        text-align: center;\n        .icon-success {\n            position: absolute;\n            top: -33rpx;\n            left: 50%;\n            margin-left: -80rpx;\n            width: 81rpx;\n            height: 66rpx;\n        }\n        .result-photo {\n            width: 120rpx;\n            height: 120rpx;\n            background: #ffffff;\n            border: 4rpx solid $deep-blue;\n            border-radius: 50%;\n            margin: 0 auto;\n            overflow: hidden;\n            .vs-me-image-img {\n                width: 100%;\n                height: 100%;\n            }\n        }\n        .result-name {\n            position: relative;\n            margin: -10rpx auto 0;\n            font-weight: bold;\n            background-color: white;\n            border-radius: 23rpx;\n            color: $main-black;\n            max-width: 160rpx;\n            font-size: 26rpx;\n            text-align: center;\n            padding: 5rpx 10rpx;\n        }\n        .result-score {\n            color: white;\n            font-size: 48rpx;\n            margin-top: 10rpx;\n            font-weight: bold;\n        }\n    }\n}\n\n.result_error {\n    background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/end_bg_3.png');\n}\n.result_ping {\n    background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/end_bg_1.png');\n}\n.share-btn-blue {\n    background-color: rgba(0, 0, 0, 0);\n    padding: 0;\n    line-height: 90rpx;\n    border-radius: 0;\n    margin-right: 70rpx;\n}\n.share-btn-red {\n    background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/end_btn_1.png');\n    background-color: rgba(0, 0, 0, 0);\n    padding: 0;\n    line-height: 90rpx;\n    border-radius: 0;\n    width: 650rpx;\n    margin: 0 auto;\n    margin-top: 40rpx;\n    margin-bottom: 240rpx;\n}\n.btn-view {\n    margin-top: 40rpx;\n\n    .again-btn {\n        background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/end_btn_2.png');\n    }\n\n    .again-no-btn {\n        color: #888888;\n        background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/end_btn_3.png');\n    }\n}\n.btn-list {\n    font-weight: normal;\n}\n.msg-view {\n    position: absolute;\n    bottom: 50rpx;\n    left: 20rpx;\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./result.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./result.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266622402\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}