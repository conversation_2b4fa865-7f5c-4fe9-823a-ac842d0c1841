{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/pk-loading/pk-loading.vue?17ce", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/pk-loading/pk-loading.vue?2709", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/pk-loading/pk-loading.vue?0e1f", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/pk-loading/pk-loading.vue?2ad3", "uni-app:///pages/pk-loading/pk-loading.vue", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/pk-loading/pk-loading.vue?dee6", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/pk-loading/pk-loading.vue?c97e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "navigationBar", "data", "baseUrl", "quest_rule_id", "initiate_user", "headimg", "nickname", "match_user", "mate_success", "musicVideo", "showBaseUrl", "user", "onLoad", "uni", "url", "that", "socket", "header", "success", "console", "fail", "socketOpen", "onReady", "onShow", "onHide", "onUnload", "clearInterval", "onPullDownRefresh", "onReachBottom", "onShareAppMessage", "title", "imageUrl", "path", "methods", "mycallback", "start", "send", "type", "user_id", "geMessage", "setTimeout", "startGame", "setRobotTime", "timer", "timeCount", "shopClose", "unitid", "httpApi", "playVideo", "useWebAudioImplement", "stopVideo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACc;;;AAGvE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAw0B,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACsD51B;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;MAEA;MACAC;MAEA;MACAC;QACAC;QACAC;MACA;MAEA;MACAC;QACAF;QACAC;MACA;MAEA;MACAE;MAEAC;MACAC;MACAC;IACA;EACA;EACA;AACA;AACA;EACAC;IACA;IACA;IACA;;IAEA;IACA;MACAC;QACAC;MACA;IACA;;IAEA;IACAC;MACAJ;IACA;IACA;MACA;MACAI;;MAEA;MACAC;QACAF;QACAG;UACA;QACA;QACAC;UACAC;QACA;QACAC;UACAD;QACA;MACA;MACA;MACAH;QACAG;QACAE;QACA;QACAN;QACAI;MACA;MACA;MACAH;QACAG;QACAE;MACA;MACA;MACAL;QACAG;QACAE;QACAF;MACA;MACA;MACAH;QACA;QACA;QACAG;QACAJ;MACA;IACA;EACA;EACA;AACA;AACA;EACAO;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;IACA;IACAC;IACAV;EACA;EACA;AACA;AACA;EACAW;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;IACA;IACA;IACA;MACAC;MACAC;MACAC;MACAd;QACA;QACAC;MACA;MACAC;QACA;MAAA;IAEA;EACA;EACAa;IACA;IACAC;MACA;MACA;MACA;QACAC;MACA;MACA;QACAA;MACA;MACA;QACA;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;UACAC;UACApC,OACA;YACAqC;YACAnC;UACA;QAEA;QACAa;UACAf;UACAiB;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAoB;MAAA;MACA;MACA;MACA;QACA;QACA;UACAnC;QACA;QACAW;UACAZ;UACAC;UACAM;QACA;QACA;QACAK;MACA;MACA;MACA;QACA;QACA;QACA;QACA;UACAX;QACA;QACA;UACAG;QACA;QACAQ;UACAX;UACAG;QACA;;QAEA;QACAQ;MACA;MACA;MACA;QACAA;UACAP;QACA;QACAgC;UACA3B;YACAC;UACA;QACA;MACA;IACA;IAEA;IACA2B;MACA;MACA;QACA;UACAJ;UACApC,OACA;YACAE;UACA;QAEA;QACAa;UACAf;UACAiB;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAuB;MACA;MACA;MACAC;QACAC;QACAzB;QACA;UACAO;UACA;YACAX;UACA;QACA;MACA;IACA;IAEA;IACA8B;MACA;MACA;MACA;QACAC;MACA;MACAC;QACA;;QAEA;UACAlC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAkC;MACA;QACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjXA;AAAA;AAAA;AAAA;AAAuiD,CAAgB,u6CAAG,EAAC,C;;;;;;;;;;;ACA3jD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pk-loading/pk-loading.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pk-loading/pk-loading.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./pk-loading.vue?vue&type=template&id=5de42906&\"\nvar renderjs\nimport script from \"./pk-loading.vue?vue&type=script&lang=js&\"\nexport * from \"./pk-loading.vue?vue&type=script&lang=js&\"\nimport style0 from \"./pk-loading.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pk-loading/pk-loading.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pk-loading.vue?vue&type=template&id=5de42906&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pk-loading.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pk-loading.vue?vue&type=script&lang=js&\"", "<template>\n    <!-- pages/pk-loading.wxml -->\n    <view class=\"main-root-bg\">\n        <navigation-bar title=\"匹配好友\" color=\"#189EF3\" />\n        <!-- loading -->\n        <view class=\"loading-container\" v-if=\"!mate_success\">\n            <view class=\"out-load load-common\">\n                <image :src=\"baseUrl + '/upload/img/loading-left.png'\" mode=\"\" class=\"loading\" />\n                <image :src=\"baseUrl + '/upload/img/loading-right.png'\" mode=\"\" class=\"loading\" />\n            </view>\n            <view class=\"in-load load-common\">\n                <image :src=\"baseUrl + '/upload/img/loading-top.png'\" mode=\"\" class=\"loading\" />\n                <image :src=\"baseUrl + '/upload/img/loading-bottom.png'\" mode=\"\" class=\"loading\" />\n            </view>\n            <view class=\"bg-pk-loading common-image\">\n                <view class=\"pk-photo\">\n                    <image v-if=\"!initiate_user.headimg\" src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250327/headimg.png\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                    <image v-else-if=\"!showBaseUrl\" :src=\"initiate_user.headimg\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                    <image v-else :src=\"baseUrl + initiate_user.headimg\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                </view>\n            </view>\n            <view class=\"pk-text\">匹配对手中...</view>\n        </view>\n        <!-- pk -->\n        <view class=\"pk-content\" v-if=\"mate_success\">\n            <view class=\"pk-me pk-common common-image animate__animated animate__fadeInLeft\">\n                <view class=\"result-pk\">\n                    <view class=\"result-photo\">\n                        <image v-if=\"!initiate_user.headimg\" src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250327/headimg.png\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                        <image v-else-if=\"!showBaseUrl\" :src=\"initiate_user.headimg\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                        <image v-else :src=\"baseUrl + initiate_user.headimg\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                    </view>\n                    <view class=\"result-name overFlow\">{{ initiate_user.nickname }}</view>\n                </view>\n            </view>\n            <view class=\"pk-text\">匹配成功，马上开始对战...</view>\n            <view class=\"pk-you pk-common common-image animate__animated animate__fadeInRight\">\n                <view class=\"result-pk\">\n                    <view class=\"result-photo\">\n                        <image v-if=\"!match_user.headimg\" src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250327/headimg.png\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                        <image v-else :src=\"baseUrl + match_user.headimg\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                    </view>\n                    <view class=\"result-name overFlow\">{{ match_user.nickname }}</view>\n                </view>\n            </view>\n            <view class=\"icon-vs-content animate__animated animate__fadeInUp\">\n                <image :src=\"baseUrl + '/upload/img/icon-vs.png'\" mode=\"\" />\n            </view>\n        </view>\n    </view>\n</template>\n\n<script>\nimport navigationBar from '@/components/navigation-bar/navigation-bar';\n// pages/pk-loading.js\nvar app = getApp();\nconst setting = require('../../http/env');\nconst httpApi = require('../../http/request'); //后续可以改为你自己的域名接口地址\nlet socket = null; //全局定义socket对象\nlet socketOpen = false; //是否开启websoket\nlet timer = null;\nexport default {\n    components: {\n        navigationBar\n    },\n    data() {\n        return {\n            //默认域名\n            baseUrl: setting.baseUrl,\n\n            //答题匹配id\n            quest_rule_id: 0,\n\n            //房主\n            initiate_user: {\n                headimg: '',\n                nickname: ''\n            },\n\n            //匹配人\n            match_user: {\n                headimg: '',\n                nickname: ''\n            },\n\n            //匹配成功\n            mate_success: false,\n\n            musicVideo: null,\n            showBaseUrl: false,\n            user: ''\n        };\n    },\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n        var that = this;\n        //用户数据\n        var user = uni.getStorageSync('userInfo');\n\n        //参数判断\n        if (!user) {\n            uni.redirectTo({\n                url: '../home/<USER>'\n            });\n        }\n\n        //保存数据\n        that.setData({\n            user: user\n        });\n        if (user) {\n            //判断是否关闭\n            that.shopClose();\n\n            // 建立 WebSocket 连接\n            socket = uni.connectSocket({\n                url: setting.ws,\n                header: {\n                    'content-type': 'application/json'\n                },\n                success(res) {\n                    console.log('WebSocket 连接成功: ', res);\n                },\n                fail(err) {\n                    console.log('WebSocket 连接失败: ', err);\n                }\n            });\n            // onOpen\n            socket.onOpen(() => {\n                console.log('打开 WebSocket 连接');\n                socketOpen = true;\n                //发送请求\n                that.send();\n                console.log(new Date());\n            });\n            // onError\n            socket.onError((err) => {\n                console.log('WebSocket 连接失败：', err);\n                socketOpen = false;\n            });\n            // onClose\n            socket.onClose((ret) => {\n                console.log('断开 WebSocket 连接', ret);\n                socketOpen = false;\n                console.log(new Date());\n            });\n            //监听接收到的消息\n            socket.onMessage((res) => {\n                let msg = res.data;\n                var getData = that.mycallback(msg);\n                console.log('接收到的服务器消息', getData);\n                that.geMessage(getData);\n            });\n        }\n    },\n    /**\n     * 生命周期函数--监听页面初次渲染完成\n     */\n    onReady() {},\n    /**\n     * 生命周期函数--监听页面显示\n     */\n    onShow() {},\n    /**\n     * 生命周期函数--监听页面隐藏\n     */\n    onHide() {},\n    /**\n     * 生命周期函数--监听页面卸载\n     */\n    onUnload() {\n        this.stopVideo();\n        clearInterval(timer);\n        socket.close();\n    },\n    /**\n     * 页面相关事件处理函数--监听用户下拉动作\n     */\n    onPullDownRefresh() {},\n    /**\n     * 页面上拉触底事件的处理函数\n     */\n    onReachBottom() {},\n    /**\n     * 用户点击右上角分享\n     */\n    onShareAppMessage(e) {\n        var user = uni.getStorageSync('userInfo');\n        var path = '/pages/home/<USER>' + user.unitid + '&unitid_copy=' + user.unitid_copy + '&inviterId=' + uni.getStorageSync('userInfo').id;\n        return {\n            title: app.globalData.shareInfo[e.from].title,\n            imageUrl: app.globalData.shareInfo[e.from].imageUrl,\n            path: path,\n            success: function (res) {\n                // 转发成功\n                console.log(11212);\n            },\n            fail: function (res) {\n                // 转发失败\n            }\n        };\n    },\n    methods: {\n        //数据回调\n        mycallback(data) {\n            var start = data.indexOf('['); // 第一次出现的位置\n            var start1 = data.indexOf('{');\n            if (start < 0) {\n                start = start1;\n            }\n            if (start >= 0 && start1 >= 0) {\n                start = Math.min(start, start1);\n            }\n            if (start >= 0) {\n                // console.log(data);\n                var json = data.substr(start); //截取\n                var json = JSON.parse(json);\n                return json;\n            } else {\n                return '';\n            }\n        },\n\n        //进行匹配\n        send() {\n            var that = this;\n            if (socketOpen) {\n                var param = {\n                    type: 'freedom',\n                    data: [\n                        {\n                            user_id: that.user.id,\n                            quest_rule_id: that.quest_rule_id\n                        }\n                    ]\n                };\n                socket.send({\n                    data: JSON.stringify(param),\n                    success: function (e) {\n                        console.log(e);\n                    }\n                });\n            }\n        },\n\n        //接收信息\n        geMessage(data) {\n            var that = this;\n            //房主创建房间展示\n            if (data && data[0] == 'quest_rule_id') {\n                var initiate_user = that.user;\n                if (initiate_user.sorce > 999) {\n                    initiate_user.sorce = '999+';\n                }\n                that.setData({\n                    quest_rule_id: data[1],\n                    initiate_user: initiate_user,\n                    showBaseUrl: !initiate_user.headimg.includes('http')\n                });\n                //开始倒计时\n                that.setRobotTime();\n            }\n            //匹配人进入房间\n            if (data && data[0] == 'data') {\n                this.playVideo();\n                var initiate_user = data[1].initiate_user;\n                var match_user = data[1].match_user;\n                if (initiate_user.sorce > 999) {\n                    initiate_user.sorce = '999+';\n                }\n                if (match_user.sorce > 999) {\n                    match_user.sorce = '999+';\n                }\n                that.setData({\n                    initiate_user: initiate_user,\n                    match_user: match_user\n                });\n\n                //开始比赛操作\n                that.startGame();\n            }\n            //开始比赛\n            if (data && data[0] == 'quest_game_id') {\n                that.setData({\n                    mate_success: true\n                });\n                setTimeout(() => {\n                    uni.redirectTo({\n                        url: '../start-fighting/start-fighting?quest_game_id=' + data[1] + '&initiate_user_id=' + this.initiate_user.id + '&match_user_id=' + this.match_user.id\n                    });\n                }, 1000);\n            }\n        },\n\n        //开始比赛\n        startGame(e) {\n            var that = this;\n            if (socketOpen) {\n                var param = {\n                    type: 'game',\n                    data: [\n                        {\n                            quest_rule_id: that.quest_rule_id\n                        }\n                    ]\n                };\n                socket.send({\n                    data: JSON.stringify(param),\n                    success: function (e) {\n                        console.log(e);\n                    }\n                });\n            }\n        },\n\n        // 匹配机器人倒计时器\n        setRobotTime() {\n            var that = this;\n            let timeCount = 2;\n            timer = setInterval(() => {\n                timeCount--;\n                console.log(timeCount);\n                if (timeCount <= 0) {\n                    clearInterval(timer);\n                    if (!that.mate_success) {\n                        that.send();\n                    }\n                }\n            }, 1000);\n        },\n\n        //是否关闭了门店\n        shopClose(e) {\n            var that = this;\n            var user = that.user;\n            var params = {\n                unitid: user.unitid\n            };\n            httpApi.Post('/index/shopClose', params).then((res) => {\n                // console.log(\"data\", res);\n\n                if (res.is_close == 1 || res.is_end == 1) {\n                    uni.redirectTo({\n                        url: '../home/<USER>'\n                    });\n                }\n            });\n        },\n\n        // 播放音频\n        playVideo() {\n            this.musicVideo = uni.createInnerAudioContext({\n                useWebAudioImplement: true\n            });\n            this.musicVideo.loop = true; // 循环\n            this.musicVideo.src = 'https://game.shuguos.com/upload/video/pk-loading.mp3';\n            this.musicVideo.play(); // 播放\n        },\n\n        // 停止播放音频并释放\n        stopVideo() {\n            if (this.musicVideo) {\n                this.musicVideo.stop(); // 停止音频资源\n                this.musicVideo.destroy(); // 释放音频资源\n            }\n        }\n    }\n};\n</script>\n<style lang=\"scss\">\n@import '../../assets/css/theme.scss';\n@import '../../assets/css/common.scss'; /* pages/pk-loading.wxss */\npage {\n    height: 100%;\n}\n.loading-container {\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin: 0 60rpx;\n    width: 620rpx;\n    height: 620rpx;\n    .load-common {\n        position: absolute;\n        top: 278rpx;\n        border-radius: 50%;\n        display: flex;\n        animation: load 2s linear infinite;\n    }\n    .out-load {\n        width: 620rpx;\n        height: 620rpx;\n        align-items: center;\n        justify-content: space-between;\n        .loading {\n            width: 148rpx;\n            height: 477rpx;\n        }\n    }\n    .in-load {\n        top: 331rpx;\n        width: 514rpx;\n        height: 514rpx;\n        align-items: center;\n        flex-flow: column;\n        justify-content: space-between;\n        animation-direction: reverse;\n        .loading {\n            width: 395rpx;\n            height: 122rpx;\n        }\n    }\n    .bg-pk-loading {\n        position: absolute;\n        top: 400rpx;\n        left: 50%;\n        transform: translateX(-50%);\n        width: 364rpx;\n        height: 364rpx;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background-image: url('https://game.shuguos.com/upload/img/bg-pk-loading.png');\n        .pk-photo {\n            width: 144rpx;\n            height: 144rpx;\n            border-radius: 50%;\n            background-color: white;\n            border: solid 3rpx $deep-blue;\n            overflow: hidden;\n            .vs-me-image-img {\n                width: 100%;\n                height: 100%;\n            }\n        }\n    }\n    .pk-text {\n        position: absolute;\n        bottom: -400rpx;\n        font-size: 28rpx;\n        color: #0078f2;\n        text-align: center;\n    }\n}\n@keyframes load {\n    0% {\n        transform: rotate(0deg);\n    }\n    100% {\n        transform: rotate(360deg);\n    }\n}\n\n/* pk*/\n.pk-content {\n    position: relative;\n    /* animation-duration: 0.5ms;*/\n    .pk-common {\n        width: 549rpx;\n        height: 989rpx;\n    }\n    .result-pk {\n        position: relative;\n        width: 160rpx;\n        .result-photo {\n            width: 120rpx;\n            height: 120rpx;\n            background: #ffffff;\n            border: 4rpx solid $deep-blue;\n            border-radius: 50%;\n            margin: 0 auto;\n            overflow: hidden;\n            .vs-me-image-img {\n                width: 100%;\n                height: 100%;\n            }\n        }\n        .result-name {\n            margin: -10rpx auto 0;\n            font-weight: bold;\n            background-color: white;\n            border-radius: 23rpx;\n            color: $main-black;\n            max-width: 160rpx;\n            text-align: center;\n        }\n        .result-score {\n            color: white;\n            opacity: 0.5;\n            font-size: 48rpx;\n            margin-top: 10rpx;\n            font-weight: bold;\n        }\n        .result-success-score {\n            color: $main-red;\n            opacity: 1;\n        }\n    }\n    .pk-me {\n        background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/loading_pk_me.png');\n        .result-pk {\n            left: 70rpx;\n            top: 320rpx;\n        }\n    }\n    .pk-you {\n        position: absolute;\n        bottom: 0;\n        right: 0;\n        background-image: url('https://game.shuguos.com/upload/img/bg-pk-you.png');\n        .result-pk {\n            left: 328rpx;\n            top: 400rpx;\n        }\n    }\n    .pk-text {\n        margin: 392rpx auto 0;\n        text-align: center;\n        font-size: 28rpx;\n        color: white;\n    }\n    .icon-vs-content {\n        position: absolute;\n        width: 100%;\n        text-align: center;\n        top: 500rpx;\n        image {\n            width: 327rpx;\n            height: 406rpx;\n        }\n    }\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pk-loading.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pk-loading.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266622349\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}