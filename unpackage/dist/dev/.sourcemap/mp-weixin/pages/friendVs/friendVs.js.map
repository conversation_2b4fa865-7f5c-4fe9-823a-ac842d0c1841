{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/friendVs/friendVs.vue?664a", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/friendVs/friendVs.vue?b524", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/friendVs/friendVs.vue?1f64", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/friendVs/friendVs.vue?8611", "uni-app:///pages/friendVs/friendVs.vue", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/friendVs/friendVs.vue?71d3", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/friendVs/friendVs.vue?60d8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "navigationBar", "remind", "data", "baseUrl", "home_user_id", "quest_rule_id", "pre_quest_game_id", "nedd_back", "initiate_user", "headimg", "nickname", "sorce", "match_user", "id", "is_fx", "is_wait", "is_start", "start_num", "showBaseUrl", "showMatchBaseUrl", "user", "showRemind", "onLoad", "uni", "url", "fx_unitid", "fx_unitid_copy", "inviterId", "that", "onReady", "onShow", "keepScreenOn", "title", "icon", "duration", "setTimeout", "onHide", "onUnload", "clearInterval", "socket", "onPullDownRefresh", "onReachBottom", "onShareAppMessage", "console", "imageUrl", "path", "success", "fail", "methods", "mycallback", "start", "send", "type", "user_id", "reconnectWs", "close<PERSON><PERSON><PERSON>", "geMessage", "startGame", "setStartTime", "timer", "timeCount", "back", "shopClose", "unitid", "httpApi", "connectWs", "header", "socketOpen"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sLAEN;AACP,KAAK;AACL;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoE11B;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,eACA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;QACAC;QACAH;QACAC;QACAF;MACA;MACA;MACAK;MACA;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACA;AACA;AACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;;IAEA;IACA;;IAEA;IACA;MACAC;QACAC,KACA,gCACAnB,gBACA,mBACAD,eACA,aACAqB,YACA,kBACAC,iBACA,gBACAC;MACA;MACA;IACA;IACA;IACA;MACAJ;QACAC;MACA;MACA;IACA;;IAEA;IACAI;MACAR;MACAhB;MACAC;MACAC;MACAC;MACAQ;MACAD;IACA;IACA;EACA;EACA;AACA;AACA;EACAe;EACA;AACA;AACA;EACAC;IAAA;IACA;MACAT;IACA;IACAE;MACAQ;IACA;IACA;MACA;IACA;IACA;MACAR;QACAS;QACAC;QACAC;MACA;MACAC;QACA;MACA;IACA;MACAZ;QACAS;QACAC;QACAC;MACA;MACAC;QACA;MACA;IACA;EACA;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;IACAC;IACA;MACAC;IACA;EACA;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;IACA;IACA;IACA;IACA;IACAd;MACAd;MACAC;IACA;IACA,WACA,4CACAV,gBACA,mBACAG,mBACA,gBACAY,cACA,qBACAA,mBACA,gBACAG;IACAoB;IACA;MACA;MACAX;MACAY;MACAC;MACAC;QACA;MAAA,CACA;MACAC;QACA;MAAA;IAEA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;QACAC;MACA;MACA;QACAA;MACA;MACA;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;UACAC;UACAlD,OACA;YACAmD;YACAhD;YACAC;UACA;QAEA;QACAqC;QACAJ;UACArC;UACA4C;QACA;MACA;IACA;IAEAQ;MACA;QACA9C;QACAU;QACAd;QACAC;QACAC;MACA;MACAiC;MACA;IACA;IAEAgB;MACA;QACAlC;MACA;IACA;IAEA;IACAmC;MACA;MACAb;MACA;MACA;QACAR;UACAZ;YACAS;YACAC;YACAC;YACAY;cACAX;gBACAZ;kBACAC;gBACA;cACA;YACA;UACA;QACA;MACA;MACA;MACA;QACAI;QACAO;UACAZ;YACAS;YACAC;YACAC;UACA;QACA;MACA;MACA;MACA;QACA;QACA;UACA1B;QACA;QACAoB;UACAvB;UACAG;UACAU;QACA;MACA;MACA;MACA;QACAU;QACAA;UACAP;QACA;MACA;MACA;MACA;QACA;QACA;QACA;UACAb;QACA;QACA;UACAI;QACA;QACAgB;UACApB;UACAU;UACAN;UACAO;QACA;QACA;UACA;UACAS;YACAd;YACAC;YACAC;UACA;UACA;UACAY;QACA;UACAU;UACAV;YACAd;YACAC;YACAC;UACA;QACA;MACA;MACA;MACA;QACAO;UACAC;QACA;MACA;IACA;IAEA;IACAiC;MACA;MACA;QACA;UACAL;UACAlD,OACA;YACAG;UACA;QAEA;QACAsC;QACAJ;UACArC;UACA4C;QACA;MACA;IACA;IAEA;IACAY;MACA;MACA;MACAC;QACAC;QACAhC;UACAX;QACA;QACA;UACAqB;UACA;YACAV;UACA;QACA;MACA;IACA;IAEA;IACAiC;MACAtC;QACAC;MACA;IACA;IAEA;IACAsC;MACA;MACA;MACA;QACAC;MACA;MACAC;QACA;UACAzC;YACAC;UACA;QACA;MACA;IACA;IAEAyC;MAAA;MACA;QACA;QACA;;QAEA;QACA1B;UACAf;UACA0C;YACA;UACA;UACApB;YACAH;UACA;UACAI;YACAJ;UACA;QACA;QACA;QACAJ;UACAI;UACAwB;UACA;UACA;UACAxB;QACA;QACA;QACAJ;UACAI;UACAwB;QACA;QACA;QACA5B;UACAI;UACAwB;QACA;QACA;QACA5B;UACA;UACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvhBA;AAAA;AAAA;AAAA;AAAqiD,CAAgB,q6CAAG,EAAC,C;;;;;;;;;;;ACAzjD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/friendVs/friendVs.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/friendVs/friendVs.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./friendVs.vue?vue&type=template&id=84d59e34&\"\nvar renderjs\nimport script from \"./friendVs.vue?vue&type=script&lang=js&\"\nexport * from \"./friendVs.vue?vue&type=script&lang=js&\"\nimport style0 from \"./friendVs.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/friendVs/friendVs.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./friendVs.vue?vue&type=template&id=84d59e34&\"", "var components\ntry {\n  components = {\n    vanPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/./miniprogram_npm/@vant/weapp/popup/index\" */ \"./miniprogram_npm/@vant/weapp/popup/index\"\n      )\n    },\n    remind: function () {\n      return import(\n        /* webpackChunkName: \"components/remind/remind\" */ \"@/components/remind/remind.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./friendVs.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./friendVs.vue?vue&type=script&lang=js&\"", "<template>\n    <view>\n        <!-- pages/friendVs/friendVs.wxml -->\n        <view class=\"main-root-bg\">\n            <navigation-bar title=\"好友对战\" color=\"#189EF3\" v-if=\"nedd_back == 0\" />\n            <navigation-bar title=\"好友对战\" color=\"#189EF3\" @back=\"back\" v-else></navigation-bar>\n            <view class=\"friend-vs dis-flex-center\">\n                <!-- 房主 -->\n                <view class=\"common-image vs-me dis-flex\">\n                    <view class=\"vs-me-image\">\n                        <image v-if=\"!initiate_user.headimg\" src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250327/headimg.png\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                        <image v-else-if=\"!showBaseUrl\" :src=\"initiate_user.headimg\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                        <image v-else :src=\"baseUrl + initiate_user.headimg\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                    </view>\n                    <view class=\"vs-name-box dis-flex-center\">\n                        <view class=\"vs-name\">{{ initiate_user.nickname }}</view>\n                        <view class=\"vs-score\">\n                            <image :src=\"baseUrl + '/upload/img/icon-score.png'\" mode=\"\" class=\"icon-score\" />\n                            <text>{{ initiate_user.sorce }}</text>\n                        </view>\n                    </view>\n                </view>\n                <!-- 邀请 -->\n                <view class=\"common-image vs-you\" v-if=\"!match_user.id\">\n                    <button open-type=\"share\" class=\"remove-btn\">\n                        <image :src=\"baseUrl + '/upload/img/invite-plus.png'\" mode=\"\" class=\"invite-plus\" />\n                    </button>\n                </view>\n                <!-- 匹配人 -->\n                <view class=\"common-image vs-you\" v-else>\n                    <view class=\"vs-name-box\">\n                        <view class=\"vs-name vs-name-1\">{{ match_user.nickname }}</view>\n                        <view class=\"vs-score vs-score-1\">\n                            <text>{{ match_user.sorce }}</text>\n                            <image :src=\"baseUrl + '/upload/img/icon-score.png'\" mode=\"\" class=\"icon-score-r\" />\n                        </view>\n                    </view>\n                    <view class=\"vs-me-image\">\n                        <image v-if=\"!match_user.headimg\" src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250327/headimg.png\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                        <image v-else-if=\"!showMatchBaseUrl\" :src=\"match_user.headimg\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                        <image v-else :src=\"baseUrl + match_user.headimg\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                    </view>\n                </view>\n            </view>\n            <view class=\"warn-info\" v-if=\"!match_user.id\">\n                <view class=\"bg-warn-info\">点击这里邀请好友进行对战哦！</view>\n            </view>\n            <view class=\"fx_text fx_text_size\" v-if=\"is_fx\">邀请好友有机会得高倍积分，快点击上方邀请按钮进行游戏吧！</view>\n            <button open-type=\"share\" class=\"fx_btn_box\" v-if=\"is_fx\">\n                <view class=\"fx_btn common-image\">快速邀请好友</view>\n            </button>\n            <view class=\"fx_text fx_text_size\" v-if=\"is_wait\">等待好友进入中。。。</view>\n            <view class=\"fx_start\" v-if=\"is_start\">\n                <view class=\"fx_text fx_text_3 fx_text_size\">{{ start_num }}</view>\n                <view class=\"fx_text fx_text_2 fx_text_size\">好友已进入</view>\n                <view class=\"fx_text_1 fx_text_size\">游戏即将开始</view>\n            </view>\n        </view>\n        <!-- 次数用尽提示 -->\n        <van-popup :show=\"showRemind\">\n            <remind @inviteNew=\"closeRemind\" :isFirend=\"true\" />\n        </van-popup>\n    </view>\n</template>\n\n<script>\nimport navigationBar from '@/components/navigation-bar/navigation-bar';\nimport remind from '@/components/remind/remind';\n// pages/friendVs/friendVs.js\nconst setting = require('../../http/env');\nconst httpApi = require('../../http/request'); //后续可以改为你自己的域名接口地址\nlet socket = null; //全局定义socket对象\nlet socketOpen = false; //是否开启websoket\nlet timer = null;\nvar app = getApp();\nexport default {\n    components: {\n        navigationBar,\n        remind\n    },\n    data() {\n        return {\n            //默认域名\n            baseUrl: setting.baseUrl,\n            //房主user_id\n            home_user_id: 0,\n            //答题匹配id\n            quest_rule_id: 0,\n            //上一个答题的ID\n            pre_quest_game_id: 0,\n            //需要返回\n            nedd_back: 0,\n            //房主\n            initiate_user: {\n                headimg: '',\n                nickname: '',\n                sorce: ''\n            },\n            //匹配人\n            match_user: {\n                id: '',\n                nickname: '',\n                sorce: '',\n                headimg: ''\n            },\n            //是否分享了\n            is_fx: true,\n            //正在匹配中\n            is_wait: false,\n            //倒计时开始\n            is_start: false,\n            start_num: 3,\n            showBaseUrl: false,\n            showMatchBaseUrl: false,\n            // 当前登录人\n            user: {},\n            // 次数用尽提示\n            showRemind: false\n        };\n    },\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n        var that = this;\n        //房主user_id\n        var home_user_id = options.home_user_id ? options.home_user_id : 0;\n        //答题匹配id\n        var quest_rule_id = options.quest_rule_id ? options.quest_rule_id : 0;\n        //分享的unitid\n        var fx_unitid = options.fx_unitid ? options.fx_unitid : '';\n        //分享的unitid_copy\n        var fx_unitid_copy = options.fx_unitid_copy ? options.fx_unitid_copy : '';\n        //分享人id\n        var inviterId = options.inviterId || '';\n\n        //上一个答题的id\n        var pre_quest_game_id = options.quest_game_id ? options.quest_game_id : 0;\n        //需要返回\n        var nedd_back = options.nedd_back ? options.nedd_back : 0;\n\n        //用户数据\n        var user = uni.getStorageSync('userInfo');\n\n        //参数判断\n        if (!user || !user.id || !user.grade_id) {\n            uni.redirectTo({\n                url:\n                    '../home/<USER>' +\n                    quest_rule_id +\n                    '&home_user_id=' +\n                    home_user_id +\n                    '&unitid=' +\n                    fx_unitid +\n                    '&unitid_copy=' +\n                    fx_unitid_copy +\n                    '&inviterId=' +\n                    inviterId\n            });\n            return;\n        }\n        //是同一个用户，不能匹配自己\n        if (home_user_id == user.id) {\n            uni.redirectTo({\n                url: '../home/<USER>'\n            });\n            return;\n        }\n\n        //保存数据\n        that.setData({\n            user: user,\n            home_user_id: home_user_id,\n            quest_rule_id: quest_rule_id,\n            pre_quest_game_id: pre_quest_game_id,\n            nedd_back: nedd_back,\n            is_wait: pre_quest_game_id ? true : false,\n            is_fx: pre_quest_game_id ? false : true\n        });\n        this.connectWs();\n    },\n    /**\n     * 生命周期函数--监听页面初次渲染完成\n     */\n    onReady() {},\n    /**\n     * 生命周期函数--监听页面显示\n     */\n    onShow() {\n        this.setData({\n            showRemind: false\n        });\n        uni.setKeepScreenOn({\n            keepScreenOn: true\n        });\n        if (!this.initiate_user) {\n            return;\n        }\n        if (!socketOpen && this.initiate_user.id === uni.getStorageSync('userInfo').id) {\n            uni.showToast({\n                title: '邀请已中断，请重新邀请好友对战',\n                icon: 'none',\n                duration: 5000\n            });\n            setTimeout(() => {\n                this.back();\n            }, 6000);\n        } else if (!socketOpen && this.match_user.id === uni.getStorageSync('userInfo').id) {\n            uni.showToast({\n                title: '操作中断，您未能成功接受邀请。您可以尝试主动邀请好友对战',\n                icon: 'none',\n                duration: 5000\n            });\n            setTimeout(() => {\n                this.back();\n            }, 6000);\n        }\n    },\n    /**\n     * 生命周期函数--监听页面隐藏\n     */\n    onHide() {},\n    /**\n     * 生命周期函数--监听页面卸载\n     */\n    onUnload() {\n        clearInterval(timer);\n        if (socket) {\n            socket.close();\n        }\n    },\n    /**\n     * 页面相关事件处理函数--监听用户下拉动作\n     */\n    onPullDownRefresh() {},\n    /**\n     * 页面上拉触底事件的处理函数\n     */\n    onReachBottom() {},\n    /**\n     * 用户点击右上角分享\n     */\n    onShareAppMessage(e) {\n        var that = this;\n        var initiate_user = that.initiate_user;\n        var quest_rule_id = that.quest_rule_id;\n        var user = that.user;\n        that.setData({\n            is_fx: false,\n            is_wait: true\n        });\n        var path =\n            '/pages/friendVs/friendVs?quest_rule_id=' +\n            quest_rule_id +\n            '&home_user_id=' +\n            initiate_user.id +\n            '&fx_unitid=' +\n            user.unitid +\n            '&fx_unitid_copy=' +\n            user.unitid_copy +\n            '&inviterId=' +\n            uni.getStorageSync('userInfo').id;\n        console.log('path---------', path);\n        return {\n            // title: initiate_user.nickname+'邀请您对战',\n            title: app.globalData.shareInfo[e.from].title,\n            imageUrl: app.globalData.shareInfo[e.from].imageUrl,\n            path: path,\n            success: function (res) {\n                // 转发成功\n            },\n            fail: function (res) {\n                // 转发失败\n            }\n        };\n    },\n    methods: {\n        //数据回调\n        mycallback(data) {\n            var start = data.indexOf('['); // 第一次出现的位置\n            var start1 = data.indexOf('{');\n            if (start < 0) {\n                start = start1;\n            }\n            if (start >= 0 && start1 >= 0) {\n                start = Math.min(start, start1);\n            }\n            if (start >= 0) {\n                var json = data.substr(start); //截取\n                var json = JSON.parse(json);\n                return json;\n            } else {\n                return '';\n            }\n        },\n\n        //进行匹配\n        send() {\n            var that = this;\n            if (socketOpen) {\n                var param = {\n                    type: 'friend',\n                    data: [\n                        {\n                            user_id: that.user.id,\n                            quest_rule_id: that.quest_rule_id,\n                            pre_quest_game_id: that.pre_quest_game_id\n                        }\n                    ]\n                };\n                console.log('ws发送数据=====', param);\n                socket.send({\n                    data: JSON.stringify(param),\n                    success: function (e) {}\n                });\n            }\n        },\n\n        reconnectWs() {\n            this.setData({\n                initiate_user: this.user,\n                showBaseUrl: !this.user.headimg.includes('http'),\n                home_user_id: '',\n                quest_rule_id: '',\n                pre_quest_game_id: ''\n            });\n            socket.close();\n            this.connectWs();\n        },\n\n        closeRemind() {\n            this.setData({\n                showRemind: false\n            });\n        },\n\n        //接收信息\n        geMessage(data) {\n            var that = this;\n            console.log('ws接到数据=====', data);\n            //失败结果展示\n            if (data && data[0] == 'msg') {\n                setTimeout(() => {\n                    uni.showToast({\n                        title: data[1],\n                        icon: 'none',\n                        duration: 5000,\n                        success: function () {\n                            setTimeout(() => {\n                                uni.redirectTo({\n                                    url: '../home/<USER>'\n                                });\n                            }, 6000);\n                        }\n                    });\n                }, 800);\n            }\n            // 需要停留在当前页面\n            if (data && data[0] == 'noMatch') {\n                that.reconnectWs();\n                setTimeout(() => {\n                    uni.showToast({\n                        title: data[1],\n                        icon: 'none',\n                        duration: 5000\n                    });\n                }, 800);\n            }\n            //房主创建房间展示\n            if (data && data[0] == 'quest_rule_id') {\n                var initiate_user = that.user;\n                if (initiate_user.sorce > 999) {\n                    initiate_user.sorce = '999+';\n                }\n                that.setData({\n                    quest_rule_id: data[1],\n                    initiate_user: initiate_user,\n                    showBaseUrl: !initiate_user.headimg.includes('http')\n                });\n            }\n            // 对战次数达到限制\n            if (data && data[0] == 'matchDataCount') {\n                that.reconnectWs();\n                that.setData({\n                    showRemind: true\n                });\n            }\n            //匹配人进入房间\n            if (data && data[0] == 'data') {\n                var initiate_user = data[1].initiate_user;\n                var match_user = data[1].match_user;\n                if (initiate_user.sorce > 999) {\n                    initiate_user.sorce = '999+';\n                }\n                if (match_user.sorce > 999) {\n                    match_user.sorce = '999+';\n                }\n                that.setData({\n                    initiate_user: initiate_user,\n                    showBaseUrl: !initiate_user.headimg.includes('http'),\n                    match_user: match_user,\n                    showMatchBaseUrl: !match_user.headimg.includes('http')\n                });\n                if (match_user.id) {\n                    //有匹配人\n                    that.setData({\n                        is_fx: false,\n                        is_wait: false,\n                        is_start: true\n                    });\n                    //开始倒计时\n                    that.setStartTime();\n                } else {\n                    clearInterval(timer);\n                    that.setData({\n                        is_fx: false,\n                        is_wait: true,\n                        is_start: false\n                    });\n                }\n            }\n            //开始比赛\n            if (data && data[0] == 'quest_game_id') {\n                uni.redirectTo({\n                    url: '../start-fighting/start-fighting?quest_game_id=' + data[1] + '&initiate_user_id=' + this.initiate_user.id + '&match_user_id=' + this.match_user.id\n                });\n            }\n        },\n\n        //开始比赛\n        startGame(e) {\n            var that = this;\n            if (socketOpen) {\n                var param = {\n                    type: 'game',\n                    data: [\n                        {\n                            quest_rule_id: that.quest_rule_id\n                        }\n                    ]\n                };\n                console.log('ws发送数据=====', param);\n                socket.send({\n                    data: JSON.stringify(param),\n                    success: function (e) {}\n                });\n            }\n        },\n\n        // 开始倒计时器\n        setStartTime() {\n            var that = this;\n            let timeCount = 3;\n            timer = setInterval(() => {\n                timeCount--;\n                that.setData({\n                    start_num: timeCount\n                });\n                if (timeCount <= 0) {\n                    clearInterval(timer);\n                    if (that.user.id == that.initiate_user.id) {\n                        that.startGame();\n                    }\n                }\n            }, 1000);\n        },\n\n        //退出提示\n        back() {\n            uni.redirectTo({\n                url: '../home/<USER>'\n            });\n        },\n\n        //是否关闭了门店\n        shopClose(e) {\n            var that = this;\n            var user = that.user;\n            var params = {\n                unitid: user.unitid\n            };\n            httpApi.Post('/index/shopClose', params).then((res) => {\n                if (res.is_close == 1 || res.is_end == 1) {\n                    uni.redirectTo({\n                        url: '../home/<USER>'\n                    });\n                }\n            });\n        },\n\n        connectWs() {\n            if (this.user) {\n                //判断是否关闭\n                this.shopClose();\n\n                // 建立 WebSocket 连接\n                socket = uni.connectSocket({\n                    url: setting.ws,\n                    header: {\n                        'content-type': 'application/json'\n                    },\n                    success(res) {\n                        console.log('WebSocket 连接成功: ', res);\n                    },\n                    fail(err) {\n                        console.log('WebSocket 连接失败: ', err);\n                    }\n                });\n                // onOpen\n                socket.onOpen(() => {\n                    console.log('打开 WebSocket 连接');\n                    socketOpen = true;\n                    //发送请求\n                    this.send();\n                    console.log(new Date());\n                });\n                // onError\n                socket.onError((err) => {\n                    console.log('WebSocket 连接失败：', err);\n                    socketOpen = false;\n                });\n                // onClose\n                socket.onClose((ret) => {\n                    console.log('断开 WebSocket 连接', ret);\n                    socketOpen = false;\n                });\n                //监听接收到的消息\n                socket.onMessage((res) => {\n                    let msg = res.data;\n                    var getData = this.mycallback(msg);\n                    this.geMessage(getData);\n                });\n            }\n        }\n    }\n};\n</script>\n<style lang=\"scss\">\n@import '../../assets/css/theme.scss';\n@import '../../assets/css/common.scss';\n@import '../../assets/css/self-animate.scss'; /* pages/friendVs/friendVs.wxss */\n\n.friend-vs {\n    margin-top: 145rpx;\n    justify-content: space-between;\n    .common-image {\n        padding: 30rpx 14rpx;\n    }\n    .vs-name-box {\n        flex-direction: column;\n    }\n    .vs-name {\n        width: 120rpx;\n        font-size: 28rpx;\n        line-height: 40rpx;\n        color: $main-black;\n        font-weight: bold;\n        margin-bottom: 8rpx;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n        overflow: hidden;\n    }\n    .vs-score {\n        position: relative;\n        width: 100%;\n        background: #fff;\n        height: 40rpx;\n        border-radius: 20rpx;\n        color: #333;\n        font-size: 28rpx;\n        line-height: 40rpx;\n        text-align: center;\n        padding: 0 20rpx 0 50rpx;\n        .icon-score {\n            position: absolute;\n            top: -2rpx;\n            left: -4rpx;\n            width: 40rpx;\n            height: 40rpx;\n        }\n        .icon-score-r {\n            position: absolute;\n            top: -2rpx;\n            right: -4rpx;\n            width: 40rpx;\n            height: 40rpx;\n        }\n    }\n\n    .vs-score-1 {\n        padding: 0 50rpx 0 10rpx;\n    }\n    .vs-name-1 {\n        text-align: right;\n    }\n\n    .vs-me {\n        padding: 40rpx 16rpx;\n        background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/wait_left_bg.png');\n        width: 340rpx;\n        height: 180rpx;\n        .vs-me-image {\n            width: 88rpx;\n            height: 88rpx;\n            border-radius: 50%;\n            background: #d9d9d9;\n            margin-right: 16rpx;\n            overflow: hidden;\n            .vs-me-image-img {\n                width: 100%;\n                height: 100%;\n            }\n        }\n    }\n    .vs-you {\n        padding: 40rpx 16rpx;\n        background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/wait_right_bg.png');\n        display: flex;\n        justify-content: flex-end;\n        width: 340rpx;\n        height: 180rpx;\n        .invite-plus {\n            width: 88rpx;\n            height: 88rpx;\n        }\n        .vs-me-image {\n            width: 88rpx;\n            height: 88rpx;\n            border-radius: 50%;\n            background: #d9d9d9;\n            margin-left: 16rpx;\n            overflow: hidden;\n            .vs-me-image-img {\n                width: 100%;\n                height: 100%;\n            }\n        }\n    }\n}\n.warn-info {\n    display: flex;\n    justify-content: flex-end;\n    padding-right: 40rpx;\n    .bg-warn-info {\n        position: relative;\n        width: 400rpx;\n        height: 126rpx;\n        padding: 24rpx;\n        background: #fff;\n        border-radius: 24rpx;\n        font-size: 28rpx;\n        line-height: 40rpx;\n        margin-top: 40rpx;\n        color: #4aaaf8;\n\n        &::after {\n            content: '';\n            position: absolute;\n            left: 300rpx;\n            top: -20rpx;\n            width: 0;\n            height: 0;\n            border-left: 10rpx solid transparent;\n            border-right: 10rpx solid transparent;\n            border-bottom: 10px solid #fff;\n        }\n    }\n}\n\n.vs-yaoqing {\n    padding: 20rpx 14rpx !important;\n}\n\n/* 分享*/\n.fx_text_size {\n    font-size: 36rpx;\n}\n.fx_text {\n    color: #fc6498;\n    width: 70%;\n    margin: 0 auto;\n    text-align: center;\n    margin-top: 150rpx;\n    line-height: 50rpx;\n    font-weight: bold;\n}\n.fx_text_1 {\n    color: #fff;\n    width: 70%;\n    margin: 0 auto;\n    text-align: center;\n    line-height: 1.5em;\n    font-weight: bold;\n}\n.fx_text_2 {\n    margin-top: 30rpx;\n}\n.fx_text_3 {\n    font-size: 100rpx;\n}\n.fx_btn_box {\n    position: fixed;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 100%;\n    bottom: 60rpx;\n    left: 0;\n    margin: 0;\n    padding: 0;\n    border: none;\n    background: none;\n}\n.fx_btn {\n    background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/end_btn_1.png');\n    width: 686rpx;\n    height: 101rpx;\n    padding-bottom: 8rpx;\n    text-align: center;\n    color: #fff;\n    font-size: 32rpx;\n    line-height: 93rpx;\n    font-weight: bold;\n}\n\n.remove-btn {\n    margin-right: 50rpx !important;\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./friendVs.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./friendVs.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266622250\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}