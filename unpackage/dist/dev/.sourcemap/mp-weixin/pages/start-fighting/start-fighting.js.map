{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/start-fighting/start-fighting.vue?7b78", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/start-fighting/start-fighting.vue?9032", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/start-fighting/start-fighting.vue?6a5b", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/start-fighting/start-fighting.vue?d772", "uni-app:///pages/start-fighting/start-fighting.vue", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/start-fighting/start-fighting.vue?44cc", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/start-fighting/start-fighting.vue?0066"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "navigationBar", "msgContact", "exitWarn", "data", "baseUrl", "quest_game_id", "game_topic_id", "answer_true", "answer", "time_over", "initiate_user", "headimg", "nickname", "sorce", "initiate_user_id", "match_user", "match_user_id", "is_right", "right_num", "showExitWarn", "countDown", "processVal", "processSpeed", "showTopicInfo", "topicName", "chooseList", "musicVideo", "all_num", "now_num", "book_name", "showBaseUrl", "user", "chooseItem", "val", "name", "chooseIndex", "onLoad", "uni", "keepScreenOn", "url", "that", "socket", "header", "success", "console", "fail", "socketOpen", "onReady", "onShow", "onHide", "onUnload", "clearInterval", "onPullDownRefresh", "onReachBottom", "onShareAppMessage", "title", "imageUrl", "path", "methods", "mycallback", "start", "send", "type", "user_id", "geMessage", "robotWin", "setCountDown", "timer", "timeCount", "closeExitWarn", "back", "chooseOption", "<PERSON><PERSON>", "setTimeout", "getIndexTt", "httpApi", "playVideo", "useWebAudioImplement", "stopVideo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACc;;;AAG3E;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uLAEN;AACP,KAAK;AACL;AACA,aAAa,sLAEN;AACP,KAAK;AACL;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAA40B,CAAgB,4yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgFh2B;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,eACA;EACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;MACA;MAEAC;MAEA;MACAC;QACAJ;QACAC;QACAC;MACA;MAEAG;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEAC;MACAC;MAEA;MACAC;QACAC;QACAC;MACA;MAEAC;MAEA;MACAC;MAEAC;MAEA;MACAC;MAEAC;MACAC;MAEAC;QACAC;QACAC;MACA;MAEAC;IACA;EACA;EACA;AACA;AACA;EACAC;IACAC;MACAC;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;MACAD;QACAE;MACA;MACA;IACA;;IAEA;IACAC;MACAT;MACA1B;MACAS;MACAE;IACA;;IAEA;IACAwB;IACA;MACA;MACAC;QACAF;QACAG;UACA;QACA;QACAC;UACAC;QACA;QACAC;UACAD;QACA;MACA;MACA;MACAH;QACAG;QACAE;QACA;QACAN;QACAI;MACA;MACA;MACAH;QACAG;QACAE;MACA;MACA;MACAL;QACAG;QACAE;QACAF;MACA;MACA;MACAH;QACA;QACA;QACAG;QACAJ;MACA;IACA;EACA;EACA;AACA;AACA;EACAO;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;IACA;IACAC;IACAV;EACA;EACA;AACA;AACA;EACAW;EACA;AACA;AACA;EACAC;EACA;AACA;AACA;EACAC;IACA;IACA;IACA;MACAC;MACAC;MACAC;MACAd;QACA;QACAC;MACA;MACAC;QACA;MAAA;IAEA;EACA;EACAa;IACA;IACAC;MACA;MACA;MACA;QACAC;MACA;MACA;QACAA;MACA;MACA;QACA;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;UACAC;UACA3D,OACA;YACA4D;YACA1D;YACAC;YACAE;YACAC;UACA;QAEA;QACAmC;QACAH;UACAtC;UACAwC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAoB;MACA;MACA;MACA;QACA;UACAxC;UACAyC;UACAxC,aACA;YACAS;YACAD;UACA,GACA;YACAC;YACAD;UACA;QAEA;QACA;UACAV;YACAW;YACAD;UACA;QACA;QACA;UACAV;YACAW;YACAD;UACA;QACA;QACA;QACAL;QACAY;UACA9B;UACAK;UACAT;UACAC;UACAC;UACAC;UACAc;UACAN;UACA;UACAG;UACAC;UACAC;UACAM;UACAC;UACAC;QACA;QACAc;QACAO;QACA;QACAX;MACA;MACA;MACA;QACAH;UACAE,KACA,oCACAC,qBACA,YACArC,gBACA,iBACAA,qBACA,oBACAA;QACA;MACA;MACA;MACA;QACA;UACA;UACAqC;YACA/B;UACA;UACA;UACA+B;QACA;MACA;IACA;IAEA;IACA0B;MAAA;MACA;MACA;MACAtB;MACAuB;QACAC;QACA;QACA5B;UACApB;UACAC;QACA;QACA;UACA;UACA;QACA;QACA;UACAmB;YACA/B;UACA;UACA;UACA;YACA+B;UACA;UACAW;UACAX;QACA;MACA;IACA;IAEA;IACA6B;MACA;QACAlD;MACA;IACA;IAEA;IACAmD;MACA1B;MACA;QACAzB;MACA;IACA;IAEA;IACAoD;MACA;MACA;QACA;QACA;QACA;QACA;UACAtD;UACAC;QACA;QACAsB;UACAhC;UACAS;UACAC;QACA;QACA;UACA;UACA;YACAsD;UACA;UACAA;QACA;UACA;QACA;QACAC;UACA;UACAjC;QACA;MACA;IACA;IAEA;IACAkC;MAAA;MACAC;QACA/B;QACA;UACAjB;QACA;MACA;IACA;IAEA;IACAiD;MACAhC;MACA;QACAiC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACAlC;MACA;QACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnhBA;AAAA;AAAA;AAAA;AAA2iD,CAAgB,26CAAG,EAAC,C;;;;;;;;;;;ACA/jD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/start-fighting/start-fighting.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/start-fighting/start-fighting.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./start-fighting.vue?vue&type=template&id=9fa169b4&\"\nvar renderjs\nimport script from \"./start-fighting.vue?vue&type=script&lang=js&\"\nexport * from \"./start-fighting.vue?vue&type=script&lang=js&\"\nimport style0 from \"./start-fighting.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/start-fighting/start-fighting.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./start-fighting.vue?vue&type=template&id=9fa169b4&\"", "var components\ntry {\n  components = {\n    vanCircle: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/./miniprogram_npm/@vant/weapp/circle/index\" */ \"./miniprogram_npm/@vant/weapp/circle/index\"\n      )\n    },\n    vanPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/./miniprogram_npm/@vant/weapp/popup/index\" */ \"./miniprogram_npm/@vant/weapp/popup/index\"\n      )\n    },\n    exitWarn: function () {\n      return import(\n        /* webpackChunkName: \"components/exit-warn/exit-warn\" */ \"@/components/exit-warn/exit-warn.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./start-fighting.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./start-fighting.vue?vue&type=script&lang=js&\"", "<template>\n    <view>\n        <!-- pages/start-fighting/start-fighting.wxml -->\n        <view class=\"main-root-bg\">\n            <navigation-bar :title=\"now_num + '/' + all_num\" color=\"#189EF3\" :delta=\"0\" @back=\"back\" />\n            <view class=\"start-fight common-image\">\n                <!-- 昵称和头像 -->\n                <view class=\"fight-info dis-flex-center\">\n                    <view class=\"fight-me common-image user-info\">\n                        <view class=\"user-photo\">\n                            <image\n                                v-if=\"!initiate_user.headimg\"\n                                src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250327/headimg.png\"\n                                mode=\"aspectFill\"\n                                class=\"vs-me-image-img\"\n                            />\n                            <image v-else-if=\"!showBaseUrl\" :src=\"initiate_user.headimg\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                            <image v-else :src=\"baseUrl + initiate_user.headimg\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                        </view>\n                        <view class=\"user-name\">{{ initiate_user.nickname }}</view>\n                        <view class=\"user-score\">{{ initiate_user.sorce }}</view>\n                    </view>\n                    <view class=\"fight-tiem common-image\">\n                        <van-circle :value=\"processVal\" :text=\"countDown\" :stroke-width=\"6\" color=\"#FFE314\" :size=\"62\" :speed=\"processSpeed\" />\n                    </view>\n                    <view class=\"fight-you common-image user-info\">\n                        <view class=\"user-photo\">\n                            <image v-if=\"!match_user.headimg\" src=\"https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250327/headimg.png\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                            <image v-else :src=\"baseUrl + match_user.headimg\" mode=\"aspectFill\" class=\"vs-me-image-img\" />\n                        </view>\n                        <view class=\"user-name\">{{ match_user.nickname }}</view>\n                        <view class=\"user-score\">{{ match_user.sorce }}</view>\n                    </view>\n                </view>\n                <!-- 题目 -->\n                <view class=\"topic-text\">{{ showTopicInfo.topicName }}</view>\n                <view class=\"as_box\">\n                    <view class=\"suc_box\" v-if=\"is_right\">\n                        <image :src=\"baseUrl + '/upload/img/suc_1.png'\" mode=\"\" class=\"suc_img\" v-if=\"right_num % 2 != 0\" />\n                        <image :src=\"baseUrl + '/upload/img/suc_2.png'\" mode=\"\" class=\"suc_img\" v-else />\n                    </view>\n                    <view class=\"choose-list\">\n                        <view\n                            @tap=\"chooseOption\"\n                            :data-item=\"chooseItem\"\n                            :data-index=\"chooseIndex\"\n                            :class=\"\n                                'choose-item dis-flex-center common-image ' +\n                                (answer && answer == chooseItem.val && answer == answer_true ? 'choose-item-yes' : '') +\n                                ' ' +\n                                (answer && answer == chooseItem.val && answer != answer_true ? 'choose-item-no' : '') +\n                                '  ' +\n                                (answer && answer != chooseItem.val && chooseItem.val == answer_true ? 'choose-item-true' : '')\n                            \"\n                            v-for=\"(chooseItem, chooseIndex) in showTopicInfo.chooseList\"\n                            :key=\"chooseIndex\"\n                        >\n                            <image :src=\"baseUrl + '/upload/img/icon-yes.png'\" mode=\"\" class=\"icon-yes\" />\n\n                            <image :src=\"baseUrl + '/upload/img/icon-no.png'\" mode=\"\" class=\"icon-no\" />\n\n                            <view>{{ chooseItem.name }}</view>\n                        </view>\n                    </view>\n                    <!-- 题目出处 -->\n                    <view class=\"book-text\">- - 出自{{ book_name }}</view>\n                </view>\n            </view>\n            <!-- <msg-contact /> -->\n        </view>\n        <van-popup :show=\"showExitWarn\">\n            <exit-warn @closeExitWarn=\"closeExitWarn\" />\n        </van-popup>\n    </view>\n</template>\n\n<script>\nimport navigationBar from '@/components/navigation-bar/navigation-bar';\nimport msgContact from '@/components/msg-contact/msg-contact';\nimport exitWarn from '@/components/exit-warn/exit-warn';\n// pages/start-fighting/start-fighting.js\nconst httpApi = require('../../http/request'); //后续可以改为你自己的域名接口地址\nconst setting = require('../../http/env');\nlet socket = null; //全局定义socket对象\nlet socketOpen = false; //是否开启websoket\nlet timer = null;\nvar app = getApp();\nexport default {\n    components: {\n        navigationBar,\n        msgContact,\n        exitWarn\n    },\n    data() {\n        return {\n            //默认域名\n            baseUrl: setting.baseUrl,\n\n            //答题比赛id\n            quest_game_id: 0,\n\n            //比赛题目id\n            game_topic_id: 0,\n\n            //正确答案\n            answer_true: '',\n\n            //答案\n            answer: '',\n\n            //是否超时\n            time_over: 0,\n\n            //房主\n            initiate_user: {\n                headimg: '',\n                nickname: '',\n                sorce: ''\n            },\n\n            initiate_user_id: '',\n\n            //匹配人\n            match_user: {\n                headimg: '',\n                nickname: '',\n                sorce: ''\n            },\n\n            match_user_id: '',\n\n            //是否回答正确\n            is_right: false,\n\n            //答对数量\n            right_num: 0,\n\n            //退出提示\n            showExitWarn: false,\n\n            //倒计时\n            countDown: 20,\n\n            processVal: 0,\n            processSpeed: 200,\n\n            //题目列表\n            showTopicInfo: {\n                topicName: '',\n                chooseList: []\n            },\n\n            musicVideo: null,\n\n            //答题数量\n            all_num: 5,\n\n            now_num: 0,\n\n            // 书名\n            book_name: '',\n\n            showBaseUrl: false,\n            user: '',\n\n            chooseItem: {\n                val: '',\n                name: ''\n            },\n\n            chooseIndex: 0\n        };\n    },\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n        uni.setKeepScreenOn({\n            keepScreenOn: true\n        });\n        var that = this;\n        //答题比赛id\n        var quest_game_id = options.quest_game_id ? options.quest_game_id : 0;\n        //用户数据\n        var user = uni.getStorageSync('userInfo');\n\n        //参数判断\n        if (!user || !user.id || !user.grade_id || !quest_game_id) {\n            uni.redirectTo({\n                url: '../home/<USER>'\n            });\n            return;\n        }\n\n        //保存数据\n        that.setData({\n            user: user,\n            quest_game_id: quest_game_id,\n            initiate_user_id: options.initiate_user_id || '',\n            match_user_id: options.match_user_id || ''\n        });\n\n        //获取标题名称\n        that.getIndexTt();\n        if (user) {\n            // 建立 WebSocket 连接\n            socket = uni.connectSocket({\n                url: setting.ws,\n                header: {\n                    'content-type': 'application/json'\n                },\n                success(res) {\n                    console.log('WebSocket 连接成功: ', res);\n                },\n                fail(err) {\n                    console.log('WebSocket 连接失败: ', err);\n                }\n            });\n            // onOpen\n            socket.onOpen(() => {\n                console.log('打开 WebSocket 连接');\n                socketOpen = true;\n                //发送请求\n                that.send();\n                console.log(new Date());\n            });\n            // onError\n            socket.onError((err) => {\n                console.log('WebSocket 连接失败：', err);\n                socketOpen = false;\n            });\n            // onClose\n            socket.onClose((ret) => {\n                console.log('断开 WebSocket 连接', ret);\n                socketOpen = false;\n                console.log(new Date());\n            });\n            //监听接收到的消息\n            socket.onMessage((res) => {\n                let msg = res.data;\n                var getData = that.mycallback(msg);\n                console.log('接收到的服务器消息', getData);\n                that.geMessage(getData);\n            });\n        }\n    },\n    /**\n     * 生命周期函数--监听页面初次渲染完成\n     */\n    onReady() {},\n    /**\n     * 生命周期函数--监听页面显示\n     */\n    onShow() {},\n    /**\n     * 生命周期函数--监听页面隐藏\n     */\n    onHide() {},\n    /**\n     * 生命周期函数--监听页面卸载\n     */\n    onUnload() {\n        this.stopVideo();\n        clearInterval(timer);\n        socket.close();\n    },\n    /**\n     * 页面相关事件处理函数--监听用户下拉动作\n     */\n    onPullDownRefresh() {},\n    /**\n     * 页面上拉触底事件的处理函数\n     */\n    onReachBottom() {},\n    /**\n     * 用户点击右上角分享\n     */\n    onShareAppMessage(e) {\n        var user = uni.getStorageSync('userInfo');\n        var path = '/pages/home/<USER>' + user.unitid + '&unitid_copy=' + user.unitid_copy + '&inviterId=' + uni.getStorageSync('userInfo').id;\n        return {\n            title: app.globalData.shareInfo[e.from].title,\n            imageUrl: app.globalData.shareInfo[e.from].imageUrl,\n            path: path,\n            success: function (res) {\n                // 转发成功\n                console.log(11212);\n            },\n            fail: function (res) {\n                // 转发失败\n            }\n        };\n    },\n    methods: {\n        //数据回调\n        mycallback(data) {\n            var start = data.indexOf('['); // 第一次出现的位置\n            var start1 = data.indexOf('{');\n            if (start < 0) {\n                start = start1;\n            }\n            if (start >= 0 && start1 >= 0) {\n                start = Math.min(start, start1);\n            }\n            if (start >= 0) {\n                // console.log(data);\n                var json = data.substr(start); //截取\n                var json = JSON.parse(json);\n                return json;\n            } else {\n                return '';\n            }\n        },\n\n        // 上报数据\n        send() {\n            var that = this;\n            if (socketOpen) {\n                var param = {\n                    type: 'match',\n                    data: [\n                        {\n                            user_id: that.user.id,\n                            quest_game_id: that.quest_game_id,\n                            game_topic_id: that.game_topic_id,\n                            answer: that.answer,\n                            time_over: that.time_over\n                        }\n                    ]\n                };\n                console.log('上报数据========', param.data);\n                socket.send({\n                    data: JSON.stringify(param),\n                    success: function (e) {\n                        console.log(e);\n                    }\n                });\n            }\n        },\n\n        //接收信息\n        geMessage(data) {\n            var that = this;\n            // 匹配到题目 开始倒计时\n            if (data && data[0] == 'data') {\n                var showTopicInfo = {\n                    topicName: data[1].name,\n                    robotWin: data[1].robot_win,\n                    chooseList: [\n                        {\n                            name: data[1].answer_a,\n                            val: 'A'\n                        },\n                        {\n                            name: data[1].answer_b,\n                            val: 'B'\n                        }\n                    ]\n                };\n                if (data[1].answer_c) {\n                    showTopicInfo.chooseList.push({\n                        name: data[1].answer_c,\n                        val: 'C'\n                    });\n                }\n                if (data[1].answer_d) {\n                    showTopicInfo.chooseList.push({\n                        name: data[1].answer_d,\n                        val: 'D'\n                    });\n                }\n                var now_num = that.now_num;\n                now_num = now_num + 1;\n                that.setData({\n                    initiate_user: data[1].initiate_user,\n                    match_user: data[1].match_user,\n                    game_topic_id: data[1].id,\n                    answer_true: data[1].answer_true,\n                    answer: '',\n                    time_over: 0,\n                    showTopicInfo: showTopicInfo,\n                    is_right: false,\n                    //倒计时\n                    countDown: 20,\n                    processVal: 0,\n                    processSpeed: 200,\n                    now_num: now_num,\n                    book_name: data[1].book_name,\n                    showBaseUrl: !data[1].initiate_user.headimg.includes('http')\n                });\n                console.log('clearInterval======');\n                clearInterval(timer);\n                this.stopVideo();\n                that.setCountDown();\n            }\n            //比赛结束\n            if (data && data[0] == 'end') {\n                uni.redirectTo({\n                    url:\n                        '../result/result?quest_game_id=' +\n                        that.quest_game_id +\n                        '&count=' +\n                        data[1].count +\n                        '&robot_flag=' +\n                        data[1].robot_flag +\n                        '&continue_flag=' +\n                        data[1].continue_flag\n                });\n            }\n            //退出比赛\n            if (data && data[0] == 'outline') {\n                if (that.answer) {\n                    //已答题、进入下一题\n                    that.setData({\n                        time_over: 1\n                    });\n                    //下一题\n                    that.send();\n                }\n            }\n        },\n\n        // 定时器\n        setCountDown() {\n            var that = this;\n            var timeCount = that.countDown;\n            console.log('setInterval======');\n            timer = setInterval(() => {\n                timeCount--;\n                var proValue = (20 - timeCount) * 5;\n                that.setData({\n                    countDown: timeCount,\n                    processVal: proValue\n                });\n                if (timeCount === 5) {\n                    // 倒计时声音\n                    this.playVideo();\n                }\n                if (timeCount <= 0) {\n                    that.setData({\n                        time_over: 1\n                    });\n                    // 倒计时结束且没有答题\n                    if (that.answer == '') {\n                        that.send();\n                    }\n                    clearInterval(timer);\n                    that.stopVideo();\n                }\n            }, 1000);\n        },\n\n        //退出提示\n        closeExitWarn() {\n            this.setData({\n                showExitWarn: false\n            });\n        },\n\n        //退出提示\n        back() {\n            console.log('exit');\n            this.setData({\n                showExitWarn: true\n            });\n        },\n\n        // 答题\n        chooseOption(e) {\n            var that = this;\n            if (!that.answer) {\n                var answer = e.currentTarget.dataset.item.val;\n                var is_right = false;\n                var right_num = that.right_num;\n                if (answer == that.answer_true) {\n                    is_right = true;\n                    right_num += 1;\n                }\n                that.setData({\n                    answer: answer,\n                    is_right: is_right,\n                    right_num: right_num\n                });\n                if (that.showTopicInfo.robotWin == 1) {\n                    var suiji = Math.floor(Math.random() * 4);\n                    if (suiji == 0) {\n                        suiji = 1;\n                    }\n                    suiji = suiji * 1000;\n                } else {\n                    var suiji = 1000;\n                }\n                setTimeout(() => {\n                    //下一题\n                    that.send();\n                }, suiji);\n            }\n        },\n\n        // 获取标题名称\n        getIndexTt() {\n            httpApi.Post('/index/getIndexTt', {}, '').then((res) => {\n                console.log(res);\n                this.setData({\n                    all_num: res.data.fight_num\n                });\n            });\n        },\n\n        // 播放音频\n        playVideo() {\n            console.log('playVideo========');\n            this.musicVideo = uni.createInnerAudioContext({\n                useWebAudioImplement: true\n            });\n            this.musicVideo.loop = true; // 循环\n            this.musicVideo.src = 'https://game.shuguos.com/upload/video/countdown.mp3';\n            this.musicVideo.play(); // 播放\n        },\n\n        // 停止播放音频并释放\n        stopVideo() {\n            console.log('stopVideo========');\n            if (this.musicVideo) {\n                this.musicVideo.stop(); // 停止音频资源\n            }\n            // if (this.data.musicVideo) {\n            //   this.data.musicVideo.destroy(); // 释放音频资源\n            // }\n        }\n    }\n};\n</script>\n<style lang=\"scss\">\n@import '../../assets/css/theme.scss';\n@import '../../assets/css/common.scss';\n@import '../../assets/css/self-animate.scss'; /* pages/start-fighting/start-fighting.wxss */\n.main-root-bg {\n    height: 100%;\n    overflow: auto;\n}\n.start-fight {\n    background: linear-gradient(#ebf6ff, #fff, #fff);\n    box-shadow: 0 8rpx 20rpx 0 rgba(0, 0, 0, 0.16);\n    border-radius: 24rpx;\n    border: 4rpx solid #4aaaf8;\n    margin: 55rpx 23rpx 140rpx 23rpx;\n    height: 1235rpx;\n    padding-top: 138rpx;\n    box-sizing: border-box;\n\n    .fight-info {\n        .fight-me {\n            background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/pk_left_bg.png');\n        }\n        .fight-tiem {\n            width: 150rpx;\n            height: 150rpx;\n            background: #5db7ff;\n            color: #4aaaf8;\n            font-size: 48rpx;\n            font-weight: bold;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            border-radius: 50%;\n        }\n        .fight-you {\n            background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/pk_right_bg.png');\n        }\n        .user-info {\n            width: 272rpx;\n            height: 210rpx;\n            box-sizing: border-box;\n            text-align: center;\n            color: white;\n            .user-photo {\n                width: 72rpx;\n                height: 72rpx;\n                border-radius: 50%;\n                margin: 20rpx auto 0;\n                background-color: white;\n                overflow: hidden;\n                .vs-me-image-img {\n                    width: 100%;\n                    height: 100%;\n                }\n            }\n            .user-name {\n                font-size: 24rpx;\n            }\n            .user-score {\n                font-size: 48rpx;\n                font-weight: bold;\n            }\n        }\n    }\n    .topic-text {\n        text-align: center;\n        margin: 56rpx 100rpx;\n        font-size: 32rpx;\n        color: #333333;\n    }\n    .choose-item {\n        position: relative;\n        background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/pk_btn_bg.png');\n        width: 533rpx;\n        height: 101rpx;\n        justify-content: center;\n        font-size: 32rpx;\n        color: #4aaaf8;\n        margin: 0 auto 40rpx;\n        .icon-yes,\n        .icon-no {\n            position: absolute;\n            left: 80rpx;\n            display: none;\n        }\n    }\n    .choose-item-yes {\n        background-image: url('https://game.shuguos.com/upload/img/bg-choose-yes.png');\n        color: white;\n        .icon-yes {\n            display: block !important;\n            width: 47rpx;\n            height: 32rpx;\n        }\n    }\n    .choose-item-no {\n        background-image: url('https://game.shuguos.com/upload/img/bg-choose-no.png');\n        color: white;\n\n        .icon-no {\n            display: block !important;\n            width: 38rpx;\n            height: 38rpx;\n        }\n    }\n    .choose-item-true {\n        background-image: url('https://game.shuguos.com/upload/img/bg-choose-yes.png');\n        color: white;\n    }\n}\n.van-circle__text {\n    color: white !important;\n}\n.as_box {\n    position: relative;\n    background: #ebf6ff;\n    padding: 80rpx 0 125rpx 0;\n}\n.suc_box {\n    position: absolute;\n    left: 50%;\n    margin-left: -185rpx;\n    width: 369rpx;\n    height: 154rpx;\n    top: -120rpx;\n    z-index: 10;\n    .suc_img {\n        width: 100%;\n        height: 100%;\n    }\n}\n\n.book-text {\n    position: absolute;\n    bottom: -8rpx;\n    left: 5rpx;\n    right: 5rpx;\n    height: 93rpx;\n    padding: 0 24rpx;\n    background: #fff;\n\n    font-size: 32rpx;\n    line-height: 93rpx;\n    text-align: right;\n}\n\nvan-circle {\n    height: 62px !important;\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./start-fighting.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./start-fighting.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266622372\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}