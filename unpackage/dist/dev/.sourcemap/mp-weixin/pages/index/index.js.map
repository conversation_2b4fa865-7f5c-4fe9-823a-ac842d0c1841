{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/index/index.vue?ef45", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/index/index.vue?e3e5", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/index/index.vue?deda", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/index/index.vue?da41", "uni-app:///pages/index/index.vue", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/index/index.vue?943b", "webpack:////Users/<USER>/Desktop/code/zsjs_xcx_v3_uni/pages/index/index.vue?a66a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "motto", "userInfo", "avatarUrl", "nick<PERSON><PERSON>", "hasUserInfo", "canIUseGetUserProfile", "canIUseNicknameComp", "methods", "bindViewTap", "onChooseAvatar", "onInputChange", "getUserProfile", "uni", "desc", "success", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiCv1B;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IAEAC;MACA;MACA;MACA;QACA;QACAL;MACA;IACA;IAEAM;MACA;MACA;MACA;QACA;QACAN;MACA;IACA;IAEAO;MAAA;MACA;MACAC;QACAC;QACA;QACAC;UACAC;UACA;YACAd;YACAG;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpFA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n    <view>\n        <!-- index.wxml -->\n        <scroll-view class=\"scrollarea\" scroll-y type=\"list\">\n            <view class=\"container\">\n                <view class=\"userinfo\">\n                    <block v-if=\"canIUseNicknameComp && !hasUserInfo\">\n                        <button class=\"avatar-wrapper\" open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\">\n                            <image class=\"avatar\" :src=\"userInfo.avatarUrl\"></image>\n                        </button>\n                        <view class=\"nickname-wrapper\">\n                            <text class=\"nickname-label\">昵称</text>\n                            <input type=\"nickname\" class=\"nickname-input\" placeholder=\"请输入昵称\" @change=\"onInputChange\" />\n                        </view>\n                    </block>\n                    <block v-else-if=\"!hasUserInfo\">\n                        <button v-if=\"canIUseGetUserProfile\" @tap=\"getUserProfile\">获取头像昵称</button>\n                        <view v-else>请使用2.10.4及以上版本基础库</view>\n                    </block>\n                    <block v-else>\n                        <image @tap=\"bindViewTap\" class=\"userinfo-avatar\" :src=\"userInfo.avatarUrl\" mode=\"cover\"></image>\n                        <text class=\"userinfo-nickname\">{{ userInfo.nickName }}</text>\n                    </block>\n                </view>\n                <view class=\"usermotto\">\n                    <text class=\"user-motto\">{{ motto }}</text>\n                </view>\n            </view>\n        </scroll-view>\n    </view>\n</template>\n\n<script>\n// index.js\nconst defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0';\nexport default {\n    data() {\n        return {\n            motto: 'Hello World',\n            userInfo: {\n                avatarUrl: defaultAvatarUrl,\n                nickName: ''\n            },\n            hasUserInfo: false,\n            canIUseGetUserProfile: uni.canIUse('getUserProfile'),\n            canIUseNicknameComp: uni.canIUse('input.type.nickname')\n        };\n    },\n    methods: {\n        bindViewTap() {},\n\n        onChooseAvatar(e) {\n            const { avatarUrl } = e.detail;\n            const { nickName } = this.userInfo;\n            this.setData({\n                'userInfo.avatarUrl': avatarUrl,\n                hasUserInfo: nickName && avatarUrl && avatarUrl !== defaultAvatarUrl\n            });\n        },\n\n        onInputChange(e) {\n            const nickName = e.detail.value;\n            const { avatarUrl } = this.userInfo;\n            this.setData({\n                'userInfo.nickName': nickName,\n                hasUserInfo: nickName && avatarUrl && avatarUrl !== defaultAvatarUrl\n            });\n        },\n\n        getUserProfile(e) {\n            // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认，开发者妥善保管用户快速填写的头像昵称，避免重复弹窗\n            uni.getUserProfile({\n                desc: '展示用户信息',\n                // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写\n                success: (res) => {\n                    console.log(res);\n                    this.setData({\n                        userInfo: res.userInfo,\n                        hasUserInfo: true\n                    });\n                }\n            });\n        }\n    }\n};\n</script>\n<style>\n/**index.wxss**/\npage {\n    height: 100vh;\n    display: flex;\n    flex-direction: column;\n}\n.scrollarea {\n    flex: 1;\n    overflow-y: hidden;\n}\n\n.userinfo {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    color: #aaa;\n    width: 80%;\n}\n\n.userinfo-avatar {\n    overflow: hidden;\n    width: 128rpx;\n    height: 128rpx;\n    margin: 20rpx;\n    border-radius: 50%;\n}\n\n.usermotto {\n    margin-top: 200px;\n}\n\n.avatar-wrapper {\n    padding: 0;\n    width: 56px !important;\n    border-radius: 8px;\n    margin-top: 40px;\n    margin-bottom: 40px;\n}\n\n.avatar {\n    display: block;\n    width: 56px;\n    height: 56px;\n}\n\n.nickname-wrapper {\n    display: flex;\n    width: 100%;\n    padding: 16px;\n    box-sizing: border-box;\n    border-top: 0.5px solid rgba(0, 0, 0, 0.1);\n    border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);\n    color: black;\n}\n\n.nickname-label {\n    width: 105px;\n}\n\n.nickname-input {\n    flex: 1;\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758266619153\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}