<template>
    <!-- components/user-info/user-info.wxml -->
    <view class="user-info dis-flex-center">
        <view class="wechat-view common-image" />
        <view class="user-image" @tap="openNick">
            <image :src="imgUrl || 'https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250327/headimg.png'" mode="aspectFill" />
        </view>
        <view class="user-score dis-flex-center" @tap="openRecord">
            <image :src="baseUrl + '/upload/img/icon-score.png'" mode="" />
            <text>{{ userInfo.sorce || 0 }}</text>
        </view>
    </view>
</template>

<script>
// components/user-info/user-info.js
const config = require('../../http/env');
const httpApi = require('../../http/request'); //后续可以改为你自己的域名接口地址

export default {
    data() {
        return {
            baseUrl: config.baseUrl,
            imgUrl: ''
            // userInfo: {},
        };
    },

    /**
     * 组件的属性列表
     */
    props: {
        userInfo: {
            type: Object,
            default: () => ({})
        }
    },

    /**
     * 组件的方法列表
     */
    methods: {
        handlePageShow: function () {
            if (!this.userInfo?.headimg) {
                return;
            }
            this.setData({
                imgUrl: this.userInfo.headimg.includes('http') ? this.userInfo.headimg : config.baseUrl + this.userInfo.headimg
            });
        },

        // 获取用户信息
        // getUserInfoByApi() {
        //   var that = this;
        //   var user = wx.getStorageSync("userInfo");
        //   httpApi.Post("/index/getUser", { user_id: user.id }).then((res) => {
        //     wx.setStorageSync("userInfo", res.data);
        //     that.setData({
        //       userInfo: res.data,
        //     });
        //   });
        // },

        // 打开积分记录
        openRecord() {
            this.$emit('openRecord', {
                detail: true
            });
        },

        // 打开头像昵称
        openNick() {
            this.$emit('openNick', {
                detail: true
            });
        }
    },

    watch: {
        userInfo: function (val) {
            if (val && val.headimg) {
                this.setData({
                    imgUrl: val.headimg.includes('http') ? val.headimg : config.baseUrl + val.headimg
                });
            }
        }
    },

    created: function () {}
};
</script>
<style lang="scss">
@import '../../assets/css/theme.scss';
@import '../../assets/css/common.scss'; /* components/user-info/user-info.wxss */
/* 用户信息+分数*/
.user-info {
    position: relative;
    margin: 0 16rpx;
    .user-image {
        width: 90rpx;
        height: 90rpx;
        border-radius: 50%;
        margin-right: 24rpx;
        background: #d9d9d9;
        image {
            width: 100%;
            height: 100%;
            border-radius: 50%;
        }
    }
    .user-score {
        border: solid 2rpx $deep-blue;
        border-radius: 56rpx;
        background: #fff;
        position: relative;
        padding: 6rpx 50rpx 6rpx 66rpx;
        color: $deep-blue;
        image {
            position: absolute;
            left: -4rpx;
            width: 60rpx;
            height: 60rpx;
            margin-right: 16rpx;
        }
        text {
            font-size: 32rpx;
            font-weight: bold;
        }
    }
    .wechat-view {
        width: 201rpx;
        height: 68rpx;
        position: absolute;
        z-index: 99;
        top: 70rpx;
        left: 40rpx;
        background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/common_info.png');
    }
}
</style>
