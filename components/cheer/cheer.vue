<template>
    <view>
        <!-- components/cheer/cheer.wxml -->
        <view class="rank-page common-image">
            <view class="warn-text">加油！</view>
            <view class="warn-text">{{ tis }}</view>
        </view>
        <view class="dis-flex-center btn-view">
            <view class="btn common-cancel-btn dis-flex" @tap="exit">继续返回</view>
            <view v-if="showContinue" class="btn common-confirm-btn dis-flex" @tap="closeChe">继续答题</view>
            <view v-else class="btn dis-flex common-btn-no">继续答题</view>
        </view>
    </view>
</template>

<script>
// components/cheer/cheer.js
export default {
    data() {
        return {
            dataNum: 2
        };
    },
    /**
     * 组件的属性列表
     */
    props: {
        tis: {
            type: String,
            default: ''
        },
        showContinue: {
            type: Boolean,
            default: true
        }
    },
    mounted() {
        // 处理小程序 attached 生命周期
        this.attached();
    },
    /**
     * 组件的方法列表
     */
    methods: {
        attached() {
            this.randomNum();
        },

        exit(e) {
            var that = this;
            uni.redirectTo({
                url: '../home/<USER>'
            });
        },

        closeChe() {
            this.$emit('closeChe', {
                detail: true
            });
        },

        randomNum() {
            const randomVal = Math.floor(Math.random() * 4) + 2;
            console.log('randomNum', randomVal);
            this.setData({
                dataNum: randomVal
            });
        }
    },
    created: function () {}
};
</script>
<style lang="scss">
@import '../../assets/css/theme.scss';
@import '../../assets/css/common.scss'; /* components/cheer/cheer.wxss */
.rank-page {
    padding-top: 200rpx;
    width: 500rpx;
    height: 394rpx;
    box-sizing: border-box;
    background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/modal_bg.png');
    text-align: center;
}

.warn-text {
    font-size: 32rpx;
    color: white;
    line-height: 1.5;
    width: 90%;
    margin: 0 auto;
    font-weight: bold;
}
.btn-view {
    margin-top: 50rpx;
    padding: 0;
    justify-content: space-between;

    .btn {
        padding: 0 40rpx;
    }
}
</style>
