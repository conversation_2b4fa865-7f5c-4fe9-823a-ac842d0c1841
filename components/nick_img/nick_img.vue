<template>
    <view>
        <!-- components/grade-list/grade-list.wxml -->
        <view class="rank-page common-modal">
            <view class="grade-name">选择头像昵称</view>
            <view class="grade-text">选择填写您的微信头像和微信昵称</view>
            <button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
                <image class="avatar" :src="avatarUrl" mode="aspectFill" />
            </button>
            <input type="nickname" :value="ch_nick" @change="bind_ch_nick" class="weui-input" placeholder-class="pl" />
        </view>
        <view class="view-btn common-cancel-btn" @tap="comfirm">确定</view>
        <view class="icon-close" @tap="closeNick">
            <image :src="baseUrl + '/upload/img/icon-close.png'" mode="" />
        </view>
    </view>
</template>

<script>
// components/grade-list/grade-list.js
const setting = require('../../http/env');
const httpApi = require('../../http/request'); //后续可以改为你自己的域名接口地址

export default {
    data() {
        return {
            //默认域名
            baseUrl: setting.baseUrl,
            avatarUrl: uni.getStorageSync('userInfo').headimg
                ? uni.getStorageSync('userInfo').headimg.includes('http')
                    ? uni.getStorageSync('userInfo').headimg
                    : setting.baseUrl + uni.getStorageSync('userInfo').headimg
                : 'https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250327/headimg.png',
            ch_nick: uni.getStorageSync('userInfo').nickname
        };
    },
    /**
     * 组件的属性列表
     */
    props: {},
    /**
     * 组件的方法列
     */
    methods: {
        // 重新获取昵称
        bind_ch_nick(e) {
            var that = this;
            that.setData({
                ch_nick: e.detail.value
            });
        },
        //重新选择头像
        onChooseAvatar(e) {
            var that = this;
            const { avatarUrl } = e.detail;
            uni.uploadFile({
                url: setting.baseUrl + '/index/upImg',
                header: {
                    'content-type': 'multipart/form-data'
                },
                method: 'POST',
                filePath: avatarUrl,
                name: 'img',
                formData: {
                    onlineid: that.user
                },
                success: function (res) {
                    //上传成功
                    if (res.data != -1) {
                        var img = res.data.replace(/[\r\n]/g, '').replace(/\ +/g, '');
                        var imgUrl = img.includes('http') ? img : setting.baseUrl + img;
                        that.setData({
                            avatarUrl: imgUrl
                        });
                    }
                }
            });
        },
        closeNick() {
            this.$emit('closeNick', {
                detail: true
            });
        },
        comfirm() {
            var that = this;
            httpApi
                .Post('/index/upWxNick', {
                    user_id: uni.getStorageSync('userInfo').id,
                    avatarUrl: that.avatarUrl,
                    nickname: that.ch_nick
                })
                .then((res) => {
                    var user = uni.getStorageSync('userInfo') || {};
                    uni.setStorageSync('userInfo', Object.assign({}, user, res.data));
                    this.$emit('closeNick', {
                        detail: true
                    });
                });
        }
    },
    created: function () {}
};
</script>
<style lang="scss">
@import '../../assets/css/theme.scss';
@import '../../assets/css/common.scss'; /* components/grade-list/grade-list.wxss */
.rank-page {
    width: 600rpx;
    padding: 40rpx 0;
    color: white;
    padding-bottom: 60rpx;
}
.grade-name {
    font-size: 40rpx;
    text-align: center;
    font-weight: bold;
}
.grade-text {
    font-size: 26rpx;
    margin: 10rpx auto;
    text-align: center;
}
.grade-list {
    flex-wrap: wrap;
    justify-content: flex-start; /* 替代原先的space-between布局方式*/
    padding: 20rpx 40rpx;
    .grade-item {
        width: 200rpx;
        height: 70rpx;
        background-color: #00a9fd;
        color: white;
        justify-content: center;
        border: solid 4rpx transparent;
        font-size: 36rpx;
        border-radius: 10rpx;
        padding: 20rpx;
        margin-bottom: 20rpx;
        &:nth-child(2n + 1) {
            margin-right: 20rpx;
        }
    }
    .grade-item-active {
        background-color: #ffe314;
        color: #e89912;
    }
}

.view-btn {
    margin: 0 auto;
    margin-top: 20rpx;
}
.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    overflow: hidden;
    margin: 0 auto;
    border-radius: 100%;
    padding: 0;
    margin-top: 20rpx;
}
.avatar {
    width: 100%;
    height: 100%;
}
.weui-input {
    color: #333;
    line-height: 30px;
    width: calc(100% - 130rpx);
    margin: 0 auto;
    height: 60rpx;
    background: #ebf6ff;
    padding: 10rpx 20rpx;
    margin-top: 30rpx;
    margin-bottom: 10rpx;
    border-radius: 100rpx;
    text-align: center;
}
.pl {
    color: #ccc;
}
.icon-close {
    text-align: center;
    image {
        width: 53rpx;
        height: 53rpx;
        margin-top: 24rpx;
    }
}
</style>
