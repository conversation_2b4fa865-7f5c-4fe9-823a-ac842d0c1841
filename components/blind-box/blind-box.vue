<template>
    <view class="blind-box">
        <image class="blind-box-title" src="https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/box_text.png" mode="" />
        <image class="blind-box-img" src="https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/box_icon.png" mode="" />
        <view class="blind-box-btn common-image" @tap="toBlindBox">快去拆开盲盒看看吧</view>
    </view>
</template>

<script>
export default {
    data() {
        return {};
    },
    methods: {
        toBlindBox() {
            console.log('34500000');
            this.$emit('toBlindBox');
        }
    },
    created: function () {}
};
</script>
<style lang="scss">
@import '../../assets/css/theme.scss';
@import '../../assets/css/common.scss';

.blind-box {
    display: flex;
    flex-direction: column;
    align-items: center;

    .blind-box-title {
        width: 597rpx;
        height: 94rpx;
    }

    .blind-box-img {
        margin: 64rpx 0;
        width: 590rpx;
        height: 408rpx;
    }

    .blind-box-btn {
        width: 670rpx;
        height: 99rpx;
        padding-bottom: 8rpx;
        background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/end_btn_1.png');

        font-size: 32rpx;
        text-align: center;
        line-height: 91rpx;
        color: #fff;
    }
}
</style>
