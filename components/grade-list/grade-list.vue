<template>
    <view>
        <!-- components/grade-list/grade-list.wxml -->
        <view class="rank-page common-modal">
            <view class="grade-name">选择年级</view>
            <view class="grade-text">比赛均根据年级匹配题库和对手</view>
            <view class="grade-list dis-flex">
                <view
                    :class="'grade-item dis-flex-center ' + (selectItem.id === item.id ? 'grade-item-active' : '')"
                    @tap="selectGrade"
                    :data-item="item"
                    v-for="(item, index) in gradeList"
                    :key="index"
                >
                    {{ item.name }}
                </view>
            </view>
        </view>
        <view class="view-btn common-cancel-btn" @tap="comfirm">确定</view>
    </view>
</template>

<script>
// components/grade-list/grade-list.js
const setting = require('../../http/env');
const httpApi = require('../../http/request'); //后续可以改为你自己的域名接口地址

export default {
    data() {
        return {
            //默认域名
            baseUrl: setting.baseUrl,
            gradeList: [],
            selectItem: {
                id: ''
            }
        };
    },
    /**
     * 组件的属性列表
     */
    props: {},
    mounted() {
        // 处理小程序 attached 生命周期
        this.attached();
    },
    /**
     * 组件的方法列表
     */
    methods: {
        attached() {
            this.getGrade();
        },

        getGrade() {
            httpApi.Post('/index/getGrade', {}).then((res) => {
                if (res.status) {
                    return;
                }
                this.setData({
                    gradeList: res.data
                });
            });
        },

        selectGrade(e) {
            console.log(e);
            this.setData({
                selectItem: e.currentTarget.dataset.item
            });
        },

        comfirm() {
            var that = this;
            if (!this.selectItem.id) {
                uni.showToast({
                    title: '请先选择年级！',
                    icon: 'none',
                    duration: 2000
                });
            } else {
                httpApi
                    .Post('/index/upGrade', {
                        user_id: uni.getStorageSync('userInfo').id,
                        grade_id: this.selectItem.id
                    })
                    .then((res) => {
                        var user = uni.getStorageSync('userInfo') || {};
                        uni.setStorageSync('userInfo', Object.assign({}, user, res.data));
                        this.$emit('closeGrade', {
                            detail: this.selectItem.id
                        });
                    });
            }
        }
    },
    created: function () {}
};
</script>
<style lang="scss">
@import '../../assets/css/theme.scss';
@import '../../assets/css/common.scss'; /* components/grade-list/grade-list.wxss */
.rank-page {
    padding: 40rpx 0;
    color: white;
}
.grade-name {
    font-size: 40rpx;
    text-align: center;
    font-weight: bold;
}
.grade-text {
    font-size: 26rpx;
    margin: 10rpx auto;
    text-align: center;
}
.grade-list {
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 20rpx 40rpx;
    .grade-item {
        width: 357rpx;
        height: 70rpx;
        color: #fff;
        justify-content: center;
        font-size: 28rpx;
        border-radius: 8rpx;
        padding: 20rpx;
        margin-bottom: 28rpx;
        border: 2rpx solid #fff;
    }
    .grade-item-active {
        background-color: #ebf6ff;
        color: #4aaaf8;
        border-color: #ebf6ff;
    }
}

.view-btn {
    margin: 0 auto;
    margin-top: 20rpx;
}
.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    overflow: hidden;
    margin: 0 auto;
    border-radius: 100%;
    padding: 0;
    margin-top: 20rpx;
}
.avatar {
    width: 100%;
    height: 100%;
}
.weui-input {
    color: #fff;
    line-height: 30px;
    width: calc(100% - 130rpx);
    margin: 0 auto;
    height: 60rpx;
    border: 4rpx solid #00a9fd;
    padding: 0 20rpx;
    margin-top: 30rpx;
    margin-bottom: 10rpx;
    border-radius: 100rpx;
    text-align: center;
}
.pl {
    color: #ccc;
}
</style>
