<template>
    <view>
        <!-- components/cheer/cheer.wxml -->
        <view class="rank-page common-image"></view>
        <view class="view-btn common-image">恭喜获{{ num }}倍积分</view>
        <view class="icon-close" @tap="closeDb">
            <image :src="baseUrl + '/upload/img/icon-close.png'" mode="" />
        </view>
        <view class="view-hei common-image"></view>
    </view>
</template>

<script>
// components/cheer/cheer.js
const setting = require('../../http/env');
export default {
    data() {
        return {
            //默认域名
            baseUrl: setting.baseUrl
        };
    },
    /**
     * 组件的属性列表
     */
    props: {
        num: {
            type: Number,
            default: 0
        }
    },
    /**
     * 组件的方法列表
     */
    methods: {
        closeDb() {
            this.$emit('closeDb', {
                detail: true
            });
        }
    },
    created: function () {}
};
</script>
<style lang="scss">
@import '../../assets/css/theme.scss';
@import '../../assets/css/common.scss'; /* components/cheer/cheer.wxss */
.rank-page {
    width: 600rpx;
    height: 600rpx;
    box-sizing: border-box;
    background-image: url('https://game.shuguos.com/upload/img/bi_shan.png');
    text-align: center;
}
.view-btn {
    display: flex;
    box-sizing: border-box;
    padding-top: 24rpx;
    justify-content: center;
    height: 100rpx;
    margin: 0 auto;
    color: white;
    font-size: 32rpx;
    margin-top: -150rpx;
}
.view-hei {
    height: 400rpx;
}
.icon-close {
    text-align: center;
    image {
        width: 53rpx;
        height: 53rpx;
        margin-top: 40rpx;
    }
}
</style>
