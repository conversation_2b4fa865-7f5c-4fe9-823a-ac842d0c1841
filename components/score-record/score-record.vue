<template>
    <view>
        <!-- components/score-record/score-record.wxml -->
        <view class="record-list common-image">
            <view class="total-score">积分：{{ totalScore }}分</view>
            <view class="record-scroll" v-if="recordList.length > 0">
                <view class="record-item dis-flex-center" v-for="(item, index) in recordList" :key="index">
                    <image src="https://game.shuguos.com/upload/img/icon-record-rank.png" mode="" class="icon-record-rank" />

                    <view class="flex-full overFlow">
                        <view class="record-name overFlow">{{ item.name }}</view>
                        <view class="record-date">{{ item.addtime }}</view>
                    </view>

                    <view class="record-score">+{{ item.sorce }}</view>
                </view>
                <view class="load-more" v-if="loadMore" @tap="loadMoreData">加载更多</view>
            </view>
            <view class="no-data-content" v-else>暂无数据~</view>
        </view>
        <view class="icon-close" @tap="closeRecord">
            <image src="https://game.shuguos.com/upload/img/icon-close.png" mode="" />
        </view>
    </view>
</template>

<script>
// components/score-record/score-record.js
const httpApi = require('../../http/request'); //后续可以改为你自己的域名接口地址

export default {
    data() {
        return {
            recordList: [],
            totalScore: 0,
            loadMore: false,
            queryParams: {
                user_id: uni.getStorageSync('userInfo').id,
                page: 1,
                limit: 6
            }
        };
    },
    /**
     * 组件的属性列表
     */
    props: {},
    mounted() {
        // 处理小程序 attached 生命周期
        this.attached();
    },
    /**
     * 组件的方法列表
     */
    methods: {
        attached() {
            this.getScoreList();
        },

        getScoreList() {
            this.setData({
                queryParams: {
                    ...this.queryParams,
                    user_id: uni.getStorageSync('userInfo').id
                }
            });
            httpApi.Post('/index/getScoreRecord', this.queryParams).then((res) => {
                if (res.status) {
                    return;
                }
                this.setData({
                    recordList: [...this.recordList, ...res.data],
                    totalScore: res.sorce,
                    loadMore: res.data.length < this.queryParams.limit ? false : true
                });
            });
        },

        loadMoreData() {
            this.queryParams.page++;
            this.setData({
                ['queryParams.page']: this.queryParams.page
            });
            this.getScoreList();
        },

        closeRecord() {
            this.$emit('closeRecord', {
                detail: true
            });
        }
    },
    created: function () {}
};
</script>
<style lang="scss">
@import '../../assets/css/theme.scss';
@import '../../assets/css/common.scss'; /* components/score-record/score-record.wxss */
.record-list {
    padding-top: 295rpx;
    width: 622rpx;
    height: 1054rpx;
    box-sizing: border-box;
    background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/score_bg.png');
}
.total-score {
    margin-bottom: 35rpx;
    font-size: 32rpx;
    color: white;
    text-align: center;
}
.record-scroll {
    padding-left: 56rpx;
    padding-right: 56rpx;
    padding-bottom: 20rpx;
    max-height: 640rpx;
    overflow-y: scroll;
    .record-item {
        border-radius: 20rpx;
        background-color: #ebf6ff;
        color: #333;
        padding: 26rpx 26rpx 26rpx 0;
        margin-bottom: 10rpx;
        .icon-record-rank {
            width: 118rpx;
            height: 70rpx;
        }
        .record-name {
            font-size: 28rpx;
            margin-bottom: 13rpx;
        }
        .record-date {
            font-size: 24rpx;
        }
        .record-score {
            color: $main-red;
            font-size: 32rpx;
        }
    }
    .load-more {
        font-size: 28rpx;
        color: white;
        text-align: center;
    }
}
.icon-close {
    text-align: center;
    image {
        width: 53rpx;
        height: 53rpx;
        margin-top: 24rpx;
    }
}
</style>
