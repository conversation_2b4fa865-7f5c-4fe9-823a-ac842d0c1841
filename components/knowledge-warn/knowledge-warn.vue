<template>
    <view>
        <!-- components/knowledge-warn/knowledge-warn.wxml -->
        <view class="rank-page common-image">
            <view class="warn-text">需要玩够{{ allnum }}次，可查看图谱</view>
            <view class="warn-text">现已玩了（{{ hasnum }}/{{ allnum }}）次</view>
        </view>
        <view class="dis-flex-center btn-view">
            <!-- <view class="btn-list common-image flex-full dis-flex share-btn">去意已决</view> -->
            <view class="btn-list common-image dis-flex confirm-btn" @tap="backHome">确定</view>
        </view>
    </view>
</template>

<script>
// components/knowledge-warn/knowledge-warn.js
export default {
    data() {
        return {};
    },
    /**
     * 组件的属性列表
     */
    props: {
        hasnum: {
            type: Number,
            default: 0
        },
        allnum: {
            type: Number,
            default: 0
        }
    },
    /**
     * 组件的方法列表
     */
    methods: {
        backHome() {
            uni.navigateBack();
        }
    },
    created: function () {}
};
</script>
<style lang="scss">
@import '../../assets/css/theme.scss';
@import '../../assets/css/common.scss'; /* components/knowledge-warn/knowledge-warn.wxss */
.rank-page {
    padding-top: 280rpx;
    width: 622rpx;
    height: 518rpx;
    box-sizing: border-box;
    background-image: url('https://game.shuguos.com/upload/img/bg-knowledge-warn.png');
}
.warn-text {
    font-size: 28rpx;
    color: white;
    line-height: 2;
    text-align: center;
}
.btn-view {
    margin-top: 50rpx;
    .share-btn {
        background-image: url('https://game.shuguos.com/upload/img/bg-white-btn.png');
        color: $blue;
    }
    .confirm-btn {
        width: 234rpx;
        margin: 0 auto;
    }
}
</style>
