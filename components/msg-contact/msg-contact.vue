<template>
    <!-- components/msg-contact/msg-contact.wxml -->
    <view class="msg-content">
        <button open-type="contact" class="remove-btn">
            <image src="https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/common_icon.png" mode="" class="icon-msg" />
        </button>
    </view>
</template>

<script>
// components/msg-contact/msg-contact.js
export default {
    data() {
        return {};
    },
    /**
     * 组件的属性列表
     */
    props: {},
    /**
     * 组件的方法列表
     */
    methods: {},
    created: function () {}
};
</script>
<style lang="scss">
@import '../../assets/css/common.scss'; /* components/msg-contact/msg-contact.wxss */

.msg-content {
    display: flex;
    text-align: left;
    /* padding-bottom: 20rpx;*/
    .icon-msg {
        width: 61rpx;
        height: 65rpx;
        margin-top: 8rpx;
        margin-left: 37rpx;
    }
}
</style>
