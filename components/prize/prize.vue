<template>
    <view>
        <!-- components/prize.wxml -->
        <view class="prize-dialog">
            <image src="https://game.shuguos.com/upload/img/prize-head.png" mode="" class="prize-head" />
        </view>
        <view class="record-list common-modal">
            <swiper class="swiper-list" :indicator-dots="true">
                <swiper-item v-for="(item, index) in prizeList" :key="index">
                    <view class="prize-text">恭喜你获得</view>

                    <image :src="baseUrl + item.img" mode="aspectFit" class="prize-example" />

                    <view class="prize-introduce">{{ item.name }}</view>
                </swiper-item>
            </swiper>
        </view>
        <view class="view-btn common-cancel-btn" @tap="viewPrize">查看</view>
        <view class="icon-close" @tap="closePrize">
            <image src="https://game.shuguos.com/upload/img/icon-close.png" mode="" />
        </view>
    </view>
</template>

<script>
// components/prize.js
const setting = require('../../http/env');
export default {
    data() {
        return {
            //默认域名
            baseUrl: setting.baseUrl
        };
    },
    /**
     * 组件的属性列表
     */
    props: {
        prizeList: {
            type: Array,
            default: () => []
        }
    },
    /**
     * 组件的方法列表
     */
    methods: {
        closePrize() {
            this.$emit('closePrize', {
                detail: true
            });
        },
        viewPrize() {
            uni.navigateTo({
                url: '/pages/prize-list/prize-list'
            });
            this.closePrize();
        }
    },
    created: function () {}
};
</script>
<style lang="scss">
@import '../../assets/css/theme.scss';
@import '../../assets/css/common.scss'; /* components/prize.wxss */
.prize-dialog {
    text-align: center;
    .prize-head {
        width: 343rpx;
        height: 260rpx;
    }
}
.record-list {
    width: 622rpx;
    height: 626rpx;
    margin-top: -148rpx;
    padding-top: 148rpx;
    text-align: center;

    .prize-text {
        font-size: 36rpx;
        color: white;
        margin-top: 50rpx;
    }
    .prize-example {
        width: 160rpx;
        height: 160rpx;
        margin-top: 58rpx;
        margin-bottom: 22rpx;
    }
    .prize-introduce {
        font-size: 28rpx;
        color: white;
    }
}
.swiper-list {
    height: 440rpx;
}
.view-btn {
    margin: 0 auto;
    margin-top: -50rpx;
}
.icon-close {
    text-align: center;
    image {
        width: 53rpx;
        height: 53rpx;
        margin-top: 24rpx;
    }
}
</style>
