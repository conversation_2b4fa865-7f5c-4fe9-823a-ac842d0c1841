<template>
    <view>
        <!-- components/rank-list/rank-list.wxml -->
        <view class="rank-page common-image">
            <view class="tab-list dis-flex-center">
                <view
                    :class="'tab-item flex-full ' + (currentTabClone === index ? 'tab-item-active' : '')"
                    @tap="selectTab"
                    :data-tab="index"
                    v-for="(item, index) in tabList"
                    :key="index"
                >
                    {{ item }}

                    <van-icon class="icon-san" name="play" v-if="currentTabClone === index" />
                </view>
            </view>
            <view class="rank-list">
                <view class="dis-flex-center list-name">
                    <view class="flex-full">排名</view>
                    <view class="flex-full">姓名</view>
                    <view class="shop_name" v-if="unitid == 'ef94c565-e98d-7228-968f685d5aa88e7d'">门店</view>
                    <view class="flex-full">积分</view>
                </view>
                <view class="scroll-rank">
                    <view class="scroll-rank-list">
                        <block v-if="rankList.length > 0">
                            <view
                                :class="'rank-item dis-flex-center bg-rank' + (index + 1) + ' ' + (index >= rewardNum ? 'bg-reword' : '')"
                                v-for="(item, index) in rankList"
                                :key="index"
                            >
                                <view class="rank-left">
                                    <image :src="'https://game.shuguos.com/upload/img/icon-rank' + (index + 1) + '.png'" mode="" class="icon-rank" v-if="index < 3" />
                                    <text class="rank-index" v-else>{{ item.ranking }}</text>
                                </view>

                                <view class="rank-name-con dis-flex-center">
                                    <image :src="item.headimg" mode="aspectFill" />
                                    <text>{{ item.nickname }}</text>
                                </view>

                                <view class="flex-full-1 shop_name" v-if="unitid == 'ef94c565-e98d-7228-968f685d5aa88e7d'">
                                    {{ item.shop }}
                                </view>

                                <view class="rank-score">{{ item.sorce }}</view>
                            </view>
                            <view class="height-auto" />
                        </block>
                        <view class="no-data-content" v-else>暂无数据~</view>
                    </view>
                    <view class="my-rank" v-if="rankList.length > 0">
                        <view class="rank-item dis-flex-center">
                            <view class="rank-left">
                                <text class="rank-index">{{ myRank.ranking }}</text>
                            </view>
                            <view class="rank-name-con dis-flex-center">
                                <image :src="myRank.headimg" mode="aspectFill" />
                                <text>{{ myRank.nickname }}</text>
                            </view>
                            <view class="flex-full-1 shop_name" v-if="unitid == 'ef94c565-e98d-7228-968f685d5aa88e7d'">
                                {{ myRank.shop }}
                            </view>
                            <view class="rank-score">{{ myRank.sorce }}</view>
                        </view>
                    </view>
                </view>
            </view>
            <view v-if="rankList.length > 0" class="wechat-view common-image" @tap="openAuth">
                <!-- <van-icon name="play" class="icon-play" />
    <view class="wechat-img" bind:tap="openAuth">使用微信头像</view> -->
            </view>
            <view class="dis-flex-center btn-view">
                <!-- open-type="share" -->
                <button class="common-image dis-flex share-btn" @tap="toFightPage">邀请好友对战赢翻倍积分</button>
            </view>
        </view>
        <view class="icon-close" @tap="closeRank">
            <image src="https://game.shuguos.com/upload/img/icon-close.png" mode="" />
        </view>
    </view>
</template>

<script>
// components/rank-list/rank-list.js
const config = require('../../http/env');
const httpApi = require('../../http/request'); //后续可以改为你自己的域名接口地址

export default {
    data() {
        return {
            tabList: ['总榜', '日榜'],
            rankList: [],

            myRank: {
                ranking: '',
                headimg: '',
                nickname: '',
                shop: '',
                sorce: ''
            },

            rewardNum: 0,
            baseUrl: config.baseUrl,
            currentTabClone: ''
        };
    },
    /**
     * 组件的属性列表
     */
    props: {
        currentTab: {
            type: Number,
            default: 0
        },
        unitid: {
            type: String,
            default: ''
        }
    },
    mounted() {
        // 处理小程序 attached 生命周期
        this.attached();
    },
    /**
     * 组件的方法列表
     */
    methods: {
        attached() {
            this.getRankList();
        },

        getRankList() {
            httpApi
                .Post('/index/getAnnounce', {
                    user_id: uni.getStorageSync('userInfo').id,
                    type: this.currentTab
                })
                .then((res) => {
                    // console.log('rankList',res.data);
                    if (res.status == 0) {
                        //处理头像地址---start---
                        const rowList = [];
                        res.data.forEach((item) => {
                            if (!item.headimg) {
                                item.headimg = 'https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250327/headimg.png';
                            } else {
                                item.headimg = !item.headimg.includes('http') ? this.baseUrl + item.headimg : item.headimg;
                            }
                            rowList.push(item);
                        });
                        if (!res.myData.headimg) {
                            res.myData.headimg = 'https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250327/headimg.png';
                        } else {
                            res.myData.headimg = !res.myData.headimg.includes('http') ? this.baseUrl + res.myData.headimg : res.myData.headimg;
                        }
                        //处理头像地址---end---

                        this.setData({
                            rankList: rowList,
                            myRank: res.myData,
                            rewardNum: res.reward_num
                        });
                    } else {
                        this.setData({
                            rankList: [],
                            myRank: {},
                            rewardNum: 0
                        });
                    }
                });
        },

        closeRank() {
            this.$emit('closeRank', {
                detail: true
            });
        },

        selectTab(e) {
            console.log('selectTab', e);
            const tabIndex = e.currentTarget.dataset.tab;
            this.setData({
                currentTabClone: tabIndex
            });
            this.getRankList();
        },

        // 去PK
        toFight() {
            this.closeRank();
            uni.navigateTo({
                url: '/pages/pk-loading/pk-loading'
            });
        },

        toFightPage() {
            this.closeRank();
            uni.navigateTo({
                url: '/pages/friendVs/friendVs'
            });
        },

        openAuth() {
            this.$emit('openNick', {
                detail: {
                    jumpIf: true
                }
            });
        }
    },
    created: function () {},
    watch: {
        currentTab: {
            handler: function (newVal, oldVal) {
                this.currentTabClone = newVal;
            },

            immediate: true
        }
    }
};
</script>
<style lang="scss">
@import '../../assets/css/theme.scss';
@import '../../assets/css/common.scss'; /* components/rank-list/rank-list.wxss */
@mixin rankBg($index: 4) {
    background-image: url('https://game.shuguos.com/upload/img/rank' + $index + '.png');
}
.rank-page {
    position: relative;
    padding-top: 312rpx;
    width: 670rpx;
    height: 1169rpx;
    box-sizing: border-box;
    background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250327/rank.png');
    .tab-list {
        margin: 0 30rpx;

        .tab-item {
            text-align: center;
            position: relative;
            color: white;
            font-size: 30rpx;
            font-weight: bold;
            .icon-san {
                font-size: 20rpx;
                position: absolute;
                bottom: -18rpx;
                left: 50%;
                transform: translateX(-50%) rotate(-90deg);
            }
        }
        .tab-item-active {
            /* color: #00a9fd;*/
        }
    }

    .rank-item {
        height: 80rpx;
        font-size: 28rpx;
        padding: 0 20rpx;
        color: white;
        margin-bottom: 13rpx;
        text-align: center;
        background: #4aaaf8;
        box-shadow: inset 0 -4rpx 0 0 #284daf;
        border-radius: 40rpx;
        border: 2rpx solid #000000;

        .rank-left {
            width: 90rpx;
        }

        .rank-index {
            font-size: 32rpx;
            font-weight: bold;
        }
        .icon-rank {
            width: 70rpx;
            height: 45rpx;
        }
        .rank-name-con {
            image {
                width: 48rpx;
                height: 48rpx;
                border-radius: 50%;
                margin-right: 14rpx;
                border: solid 2rpx red;
            }
            text {
                width: 150rpx;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                text-align: left;
            }
        }

        .rank-score {
            width: 100rpx;
        }
    }

    .my-rank {
        position: absolute;
        bottom: 0;
        left: -8rpx;
        right: -8rpx;
        box-sizing: border-box;
        padding: 15rpx 8rpx;
        background-color: #ebf6ff;
        border-radius: 0 0 24rpx 24rpx;
        .rank-item {
            background: #fff;
            box-shadow: inset 0 -4rpx 0 0 #aaa;
            border-radius: 40rpx;
            border: 2rpx solid #000000;
            height: 80rpx;
            color: $main-black;
            margin-bottom: 0;
        }
    }

    .rank-list {
        margin: 24rpx 58rpx 0;
        padding: 0 8rpx;
        background: #ebf6ff;
        border-radius: 24rpx;

        .list-name {
            text-align: center;
            color: #333333;
            font-size: 26rpx;
            line-height: 76rpx;
        }
        .scroll-rank {
            position: relative;
        }
        .scroll-rank-list {
            height: 560rpx;
            max-height: 560rpx;
            overflow-y: scroll;

            .bg-rank1 {
                background: #ff669a;
                box-shadow: inset 0 -4rpx 0 0 #b22468;
                border-radius: 40rpx;
                border: 2rpx solid #000000;
            }
            .bg-rank2 {
                background: #ffe314;
                box-shadow: inset 0 -4rpx 0 0 #e27f11;
                border-radius: 40rpx;
                border: 2rpx solid #000000;
            }
            .bg-rank3 {
                background: #44cc89;
                box-shadow: inset 0 -4rpx 0 0 #16806c;
                border-radius: 40rpx;
                border: 2rpx solid #000000;
            }
            .height-auto {
                height: 100rpx;
            }
            .bg-reword {
                /* @include rankBg(4);*/
                background: #4aaaf8;
                box-shadow: inset 0 -4rpx 0 0 #284daf;
                border-radius: 40rpx;
                border: 2rpx solid #000000;
            }
        }
    }

    .wechat-view {
        width: 201rpx;
        height: 68rpx;
        position: absolute;
        bottom: 104rpx;
        left: 150rpx;
        background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/common_info.png');
    }
}
.icon-close {
    margin-top: 20rpx;
    text-align: center;
    image {
        width: 53rpx;
        height: 53rpx;
        margin-top: 24rpx;
    }
}

.share-btn {
    width: 448rpx;
    box-sizing: border-box;
    height: 93rpx;
    color: white;
    font-size: 32rpx;
    font-weight: bold;
    justify-content: center;
    padding-top: 24rpx;

    line-height: normal;
    background-color: transparent;
    text-align: center;
    margin: 90rpx auto 0;
    background: #fc6498;
    box-shadow: 0 8rpx 0 0 #b22468;
    border-radius: 46rpx;
}

.flex-full-1 {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.shop_name {
    width: 120rpx;
}
</style>
