<template>
    <view>
        <!-- components/exit-warn/exit-warn.wxml -->
        <view class="rank-page common-modal">
            <view class="warn-text">确认离开嘛？</view>
            <view class="warn-text text-sm">好孩子是不会轻言放弃的哦~</view>
            <image src="https://game.shuguos.com/upload/img/icon-fail.png" mode="" class="icon-fail" />
        </view>
        <view class="dis-flex-center btn-view">
            <view class="btn common-cancel-btn dis-flex" @tap="exit">去意已决</view>
            <view class="btn common-confirm-btn dis-flex" @tap="closeExit">继续PK</view>
        </view>
        <view class="icon-close" @tap="closeExit">
            <image src="https://game.shuguos.com/upload/img/icon-close.png" mode="" />
        </view>
    </view>
</template>

<script>
// components/exit-warn/exit-warn.js
export default {
    data() {
        return {};
    },
    /**
     * 组件的属性列表
     */
    props: {},
    /**
     * 组件的方法列表
     */
    methods: {
        closeExit() {
            this.$emit('closeExitWarn', {
                detail: true
            });
        },
        exit() {
            uni.redirectTo({
                url: '../home/<USER>'
            });
        }
    },
    created: function () {}
};
</script>
<style lang="scss">
@import '../../assets/css/theme.scss';
@import '../../assets/css/common.scss'; /* components/exit-warn/exit-warn.wxss */
.rank-page {
    width: 520rpx;
    padding-top: 80rpx;
    height: 408rpx;
    box-sizing: border-box;
    text-align: center;
    .icon-fail {
        width: 128rpx;
        height: 128rpx;
        margin: 24rpx auto;
    }
}

.warn-text {
    font-size: 36rpx;
    color: white;
    line-height: 1.5;
}
.text-sm {
    font-size: 28rpx;
}
.btn-view {
    margin-top: 50rpx;
    padding: 0 40rpx;
    justify-content: space-between;

    .btn {
        padding: 0 40rpx;
    }
}

.icon-close {
    text-align: center;
    image {
        width: 53rpx;
        height: 53rpx;
        margin-top: 24rpx;
    }
}
</style>
