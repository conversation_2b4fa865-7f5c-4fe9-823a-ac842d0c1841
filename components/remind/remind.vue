<template>
    <view>
        <view :class="isFirend ? 'remind-page common-image remind-page-firend' : 'remind-page common-image'">
            <view class="remind-text" v-if="!isFirend">今日随机对战次数已用尽。如果想要继续答题赢积分，快邀请好友对战吧</view>
            <view class="remind-text" v-else>今天你已经跟该好友对战3次，想要继续赢积分快去邀请新的好友吧！</view>
            <view class="remind-btn common-image" v-if="!isFirend" @tap="shopClose">邀请好友对战赢翻倍积分</view>
            <button class="fx_btn_box" :open-type="noShare ? '' : 'share'" @tap="inviteNew" v-else>
                <view class="remind-btn2 common-image">邀请新的好友对战赢翻倍积分</view>
            </button>
        </view>
        <view v-if="!isFirend" class="icon-close" @tap="closeRemind">
            <image src="https://game.shuguos.com/upload/img/icon-close.png" mode="" />
        </view>
        <view v-else class="back-btn" @tap="goHome">返回首页</view>
    </view>
</template>

<script>
export default {
    data() {
        return {};
    },
    /**
     * 组件的属性列表
     */
    props: {
        // 是否是好友对战
        isFirend: {
            type: Boolean,
            default: false
        },
        // 是否需要分享
        noShare: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        shopClose() {
            this.$emit('shopClose');
        },
        closeRemind() {
            this.$emit('closeRemind');
        },
        goHome() {
            uni.redirectTo({
                url: '../home/<USER>'
            });
        },
        inviteNew() {
            this.$emit('inviteNew');
        }
    },
    created: function () {}
};
</script>
<style lang="scss">
@import '../../assets/css/theme.scss';
@import '../../assets/css/common.scss';

.remind-page {
    position: relative;
    width: 598rpx;
    height: 669rpx;
    padding: 147rpx 139rpx 0 99rpx;
    margin: 0 36rpx 0 52rpx;
    background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/info_bg.png');

    .remind-text {
        font-size: 32rpx;
        line-height: 38rpx;
        text-align: center;
        color: #a56f2f;
    }

    .fx_btn_box {
        position: absolute;
        bottom: -62rpx;
        left: -52rpx;
        width: 686rpx;
        height: 138rpx;
        background: none;

        .remind-btn2 {
            width: 100%;
            height: 100%;
            padding-bottom: 12rpx;
            background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/info_btn.png');

            font-size: 48rpx;
            font-weight: bold;
            text-align: center;
            line-height: 126rpx;
            color: #fff;
        }
    }

    .remind-btn {
        position: absolute;
        bottom: -62rpx;
        left: -52rpx;
        width: 686rpx;
        height: 138rpx;
        padding-bottom: 12rpx;
        background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/info_btn.png');

        font-size: 56rpx;
        font-weight: bold;
        text-align: center;
        line-height: 126rpx;
        color: #fff;
    }
}

.remind-page-firend {
    background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250327/count_bg.png');
}

.icon-close {
    margin-top: 80rpx;
    text-align: center;
    image {
        width: 53rpx;
        height: 53rpx;
        margin-top: 24rpx;
    }
}

.back-btn {
    padding-top: 100rpx;
    font-size: 36rpx;
    font-weight: bold;
    color: #fff;
    line-height: 50rpx;
    text-align: center;
}
</style>
