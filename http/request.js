//后续可以改为你自己的域名接口地址
const config = require('./env.js');
var sha1 = require('./sha1.js');
var md5 = require('./md5.js');
const baseUrl = config.baseUrl;
const signCode = config.signCode;
var timeStamp = parseInt(new Date().getTime() / 1000);
var randomStr = Math.random().toString(36).substr(2, 15);
var sign = md5.hex_md5(sha1.hex_sha1(timeStamp + randomStr + signCode));
const http = ({ url, param, tis, method }) => {
    if (tis != '') {
        uni.showLoading({
            title: tis
        });
    }
    // let timeStart = Date.now();
    return new Promise((resolve, reject) => {
        console.log(url + '接口入参：', param);
        uni.request({
            url: baseUrl + url,
            data: param,
            method: method,
            header: {
                'content-type': 'application/json',
                // 默认值 ,另一种是 "application/json"
                timeStamp: timeStamp,
                randomStr: randomStr,
                sign: sign
            },
            complete: (res) => {
                console.log(url + '接口返参：', res);
                if (tis != '') {
                    uni.hideLoading();
                }
                // console.log(`耗时${Date.now() - timeStart}`);
                if (res.statusCode >= 200 && res.statusCode < 300) {
                    resolve(res.data);
                } else {
                    reject(res);
                }
            }
        });
    });
};
// get方法
const _get = (url, param = {}, tis = '请求数据中...') => {
    return http({
        url,
        param,
        tis,
        method: 'GET'
    });
};

//post方法
const _post = (url, param = {}, tis = '请求数据中...') => {
    return http({
        url,
        param,
        tis,
        method: 'POST'
    });
};
module.exports = {
    Get: _get,
    Post: _post
};
