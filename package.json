{"name": "zsjs_xcx_v3_uni", "version": "1.0.0", "description": "知识竞赛小程序uni-app版本", "main": "main.js", "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-360": "cross-env NODE_ENV=production UNI_PLATFORM=mp-360 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-kuaishou": "cross-env NODE_ENV=production UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "build:quickapp-native": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-native vue-cli-service uni-build", "build:quickapp-webview": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview vue-cli-service uni-build", "dev": "npm run dev:mp-weixin", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-360": "cross-env NODE_ENV=development UNI_PLATFORM=mp-360 vue-cli-service uni-build --watch", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-kuaishou": "cross-env NODE_ENV=development UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "dev:quickapp-native": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-native vue-cli-service uni-build --watch", "dev:quickapp-webview": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "dependencies": {"@dcloudio/uni-app": "^2.0.2", "@dcloudio/uni-h5": "^2.0.2", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-mp-360": "^2.0.2", "@dcloudio/uni-mp-alipay": "^2.0.2", "@dcloudio/uni-mp-baidu": "^2.0.2", "@dcloudio/uni-mp-kuaishou": "^2.0.2", "@dcloudio/uni-mp-qq": "^2.0.2", "@dcloudio/uni-mp-toutiao": "^2.0.2", "@dcloudio/uni-mp-weixin": "^2.0.2", "@dcloudio/uni-quickapp-native": "^2.0.2", "@dcloudio/uni-quickapp-webview": "^2.0.2", "@dcloudio/uni-stat": "^2.0.2", "@vue/shared": "^3.0.0", "core-js": "^3.8.3", "vue": "^2.6.11", "vuex": "^3.2.0", "@vant/weapp": "^1.11.4"}, "devDependencies": {"@dcloudio/types": "^2.5.16", "@dcloudio/uni-automator": "^2.0.2", "@dcloudio/uni-cli-shared": "^2.0.2", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.2", "@dcloudio/vue-cli-plugin-uni": "^2.0.2", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.2", "@dcloudio/webpack-uni-mp-loader": "^2.0.2", "@dcloudio/webpack-uni-pages-loader": "^2.0.2", "@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-service": "~4.5.15", "babel-plugin-import": "^1.11.0", "cross-env": "^7.0.2", "jest": "^25.4.0", "mini-types": "*", "miniprogram-api-typings": "*", "postcss-comment": "^2.0.0"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}, "license": "MIT"}