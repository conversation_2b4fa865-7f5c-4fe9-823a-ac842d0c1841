.van-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.van-multi-ellipsis--l2 {
    -webkit-line-clamp: 2;
}
.van-multi-ellipsis--l2,
.van-multi-ellipsis--l3 {
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
}
.van-multi-ellipsis--l3 {
    -webkit-line-clamp: 3;
}
.van-clearfix:after {
    clear: both;
    content: '';
    display: table;
}
.van-hairline,
.van-hairline--bottom,
.van-hairline--left,
.van-hairline--right,
.van-hairline--surround,
.van-hairline--top,
.van-hairline--top-bottom {
    position: relative;
}
.van-hairline--bottom:after,
.van-hairline--left:after,
.van-hairline--right:after,
.van-hairline--surround:after,
.van-hairline--top-bottom:after,
.van-hairline--top:after,
.van-hairline:after {
    border: 0 solid #ebedf0;
    bottom: -50%;
    box-sizing: border-box;
    content: ' ';
    left: -50%;
    pointer-events: none;
    position: absolute;
    right: -50%;
    top: -50%;
    transform: scale(0.5);
    transform-origin: center;
}
.van-hairline--top:after {
    border-top-width: 1px;
}
.van-hairline--left:after {
    border-left-width: 1px;
}
.van-hairline--right:after {
    border-right-width: 1px;
}
.van-hairline--bottom:after {
    border-bottom-width: 1px;
}
.van-hairline--top-bottom:after {
    border-width: 1px 0;
}
.van-hairline--surround:after {
    border-width: 1px;
}
