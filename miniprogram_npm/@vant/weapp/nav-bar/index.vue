<template>
    <view>
        <view v-if="fixed && placeholder" :style="'height: ' + height + 'px;'" />

        <view
            :class="utils.bem('nav-bar', { fixed }) + ' custom-class ' + (border ? 'van-hairline--bottom' : '')"
            :style="computed.barStyle({ zIndex, statusBarHeight, safeAreaInsetTop }) + '; ' + customStyle"
        >
            <view class="van-nav-bar__content">
                <view class="van-nav-bar__left" @tap="onClickLeft">
                    <block v-if="leftArrow || leftText">
                        <van-icon v-if="leftArrow" size="16px" name="arrow-left" custom-class="van-nav-bar__arrow" />
                        <view v-if="leftText" class="van-nav-bar__text" hover-class="van-nav-bar__text--hover" hover-stay-time="70">{{ leftText }}</view>
                    </block>
                    <slot v-else name="left" />
                </view>
                <view class="van-nav-bar__title title-class van-ellipsis">
                    <block v-if="title">{{ title }}</block>
                    <slot v-else name="title" />
                </view>
                <view class="van-nav-bar__right" @tap="onClickRight">
                    <view v-if="rightText" class="van-nav-bar__text" hover-class="van-nav-bar__text--hover" hover-stay-time="70">{{ rightText }}</view>
                    <slot v-else name="right" />
                </view>
            </view>
        </view>
    </view>
</template>
<script module="utils" lang="wxs" src="@/miniprogram_npm/@vant/weapp/wxs/utils.wxs"></script>
<script module="computed" lang="wxs" src="@/miniprogram_npm/@vant/weapp/nav-bar/index.wxs"></script>
<script>
'use strict';
Object.defineProperty(exports, '__esModule', {
    value: true
});
var component_1 = require('../common/component');
var utils_1 = require('../common/utils');
export default {
    data() {
        return {
            height: 46,
            statusBarHeight: ''
        };
    },
    classes: ['title-class'],
    props: {
        title: String,
        fixed: {
            type: Boolean
        },
        placeholder: {
            type: Boolean
        },
        leftText: String,
        rightText: String,
        customStyle: String,
        leftArrow: Boolean,
        border: {
            type: Boolean,
            default: true
        },
        zIndex: {
            type: Number,
            default: 1
        },
        safeAreaInsetTop: {
            type: Boolean,
            default: true
        }
    },
    created: function () {
        var statusBarHeight = (0, utils_1.getSystemInfoSync)().statusBarHeight;
        this.setData({
            statusBarHeight: statusBarHeight,
            height: 46 + statusBarHeight
        });
    },
    mounted: function () {
        this.setHeight();
    },
    methods: {
        onClickLeft: function () {
            this.$emit('click-left');
        },
        onClickRight: function () {
            this.$emit('click-right');
        },
        setHeight: function () {
            var that = this;
            if (!this.fixed || !this.placeholder) {
                return;
            }
            this.$nextTick(function () {
                (0, utils_1.getRect)(that, '.van-nav-bar').then(function (res) {
                    if (res && 'height' in res) {
                        that.setData({
                            height: res.height
                        });
                    }
                });
            });
        }
    },
    watch: {
        fixed: {
            handler: function () {
                var that = this;
                if (!this.fixed || !this.placeholder) {
                    return;
                }
                this.$nextTick(function () {
                    (0, utils_1.getRect)(that, '.van-nav-bar').then(function (res) {
                        if (res && 'height' in res) {
                            that.setData({
                                height: res.height
                            });
                        }
                    });
                });
            },

            immediate: true
        },

        placeholder: {
            handler: function () {
                var that = this;
                if (!this.fixed || !this.placeholder) {
                    return;
                }
                this.$nextTick(function () {
                    (0, utils_1.getRect)(that, '.van-nav-bar').then(function (res) {
                        if (res && 'height' in res) {
                            that.setData({
                                height: res.height
                            });
                        }
                    });
                });
            },

            immediate: true
        }
    }
};
</script>
<style>
@import '../common/index.css';
.van-nav-bar {
    background-color: var(--nav-bar-background-color, #fff);
    box-sizing: initial;
    height: var(--nav-bar-height, 46px);
    line-height: var(--nav-bar-height, 46px);
    position: relative;
    text-align: center;
    -webkit-user-select: none;
    user-select: none;
}
.van-nav-bar__content {
    height: 100%;
    position: relative;
}
.van-nav-bar__text {
    color: var(--nav-bar-text-color, #1989fa);
    display: inline-block;
    margin: 0 calc(var(--padding-md, 16px) * -1);
    padding: 0 var(--padding-md, 16px);
    vertical-align: middle;
}
.van-nav-bar__text--hover {
    background-color: #f2f3f5;
}
.van-nav-bar__arrow {
    color: var(--nav-bar-icon-color, #1989fa) !important;
    font-size: var(--nav-bar-arrow-size, 16px) !important;
    vertical-align: middle;
}
.van-nav-bar__arrow + .van-nav-bar__text {
    margin-left: -20px;
    padding-left: 25px;
}
.van-nav-bar--fixed {
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
}
.van-nav-bar__title {
    color: var(--nav-bar-title-text-color, #323233);
    font-size: var(--nav-bar-title-font-size, 16px);
    font-weight: var(--font-weight-bold, 500);
    margin: 0 auto;
    max-width: 60%;
}
.van-nav-bar__left,
.van-nav-bar__right {
    align-items: center;
    bottom: 0;
    display: flex;
    font-size: var(--font-size-md, 14px);
    position: absolute;
    top: 0;
}
.van-nav-bar__left {
    left: var(--padding-md, 16px);
}
.van-nav-bar__right {
    right: var(--padding-md, 16px);
}
</style>
