/* common*/
page {
    height: 100%;
    /* background-color: #584db1 !important;*/
}
view,
text {
    box-sizing: border-box;
}
button {
    padding: 0;
    margin: 0;
    border: none;

    &::after {
        border: none;
    }
}
.common-image {
    background-repeat: no-repeat;
    background-size: 100% 100%;
}
.main-root-bg {
    height: 100%;
    background-image: url('https://zsmatch.oss-cn-qingdao.aliyuncs.com/v3/20250318/common_bg.png');
    overflow: scroll;
    @extend .common-image;
}
/* 首页的动画gif*/
.home-icon-gif {
    width: 523rpx;
    height: 544rpx;
    margin: 0 auto;
}
/* flex布局*/
.dis-flex {
    display: flex;
}
.dis-flex-center {
    @extend .dis-flex;
    align-items: center;
}
.flex-full {
    flex: 1;
}

/* 文本溢出 */
.overFlow {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.overFlow2 {
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* 去掉特殊button的样式*/
.remove-btn {
    width: auto !important;
    height: auto !important;
    background: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    &::after {
        border: none !important;
    }
}

/* 去掉popup的背景色*/
.van-popup {
    background-color: transparent !important;
}

.van-progress {
    background-color: rgba($color: white, $alpha: 0.1) !important;
}

.btn-view {
    padding: 0 50rpx;
}
.btn-list {
    width: 234rpx;
    box-sizing: border-box;
    height: 100rpx;
    color: white;
    font-size: 32rpx;
    font-weight: bold;
    justify-content: center;
    padding-top: 24rpx;
}
.share-btn {
    margin-right: 70rpx;
}

/* no-data-content*/
.no-data-content {
    font-size: 28rpx;
    color: #fff;
    text-align: center;
    margin: 50rpx;
}

.common-confirm-btn {
    width: fit-content;
    height: 92rpx;
    padding: 0 72rpx;
    background: #4aaaf8;
    box-shadow: 0 8rpx 0 0 #2571ae;
    border-radius: 46rpx;
    margin-bottom: 8rpx !important;

    font-size: 32rpx;
    color: #fff;
    line-height: 92rpx;
}

.common-cancel-btn {
    @extend .common-confirm-btn;
    background: #fff;
    box-shadow: 0 8rpx 0 0 #d8d8d8;

    color: #4aaaf8;
}

.common-btn-no {
    @extend .common-confirm-btn;
    background: #f0f0f0;
    box-shadow: 0 8rpx 0 0 #cbcbcb;

    color: #888888;
}

.common-modal {
    background: #4aaaf8;
    box-shadow: 0 8rpx 0 0 #283ba8;
    border-radius: 24rpx;
}
